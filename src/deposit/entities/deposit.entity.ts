import { DecimalJsTransformer } from "common/util/decimal-js-transformer";
import { Platform } from "src/platform/entities/platform.entity";
import { User } from "src/user/entities/user.entity";
import { Column, CreateDateColumn, Entity, JoinColumn, ManyToOne, PrimaryGeneratedColumn, Timestamp, UpdateDateColumn } from "typeorm";

@Entity()
export class Deposit {
    @PrimaryGeneratedColumn({ type: 'bigint' })
    id: number

    @ManyToOne(() => Platform, { nullable: false })
    @JoinColumn({ name: 'platform_id' })
    platform: Platform

    @ManyToOne(() => User, { nullable: false })
    @JoinColumn({ name: 'user_id' })
    user: User

    @Column({ type: 'bigint' })
    deposit_id: number

    @Column()
    coin: string

    @Column({
        default: '0',
        type: 'decimal',
        precision: 25,
        scale: 8,
        transformer: DecimalJsTransformer,
    })
    qty: string

    @CreateDateColumn()
    created_at: Date

    @UpdateDateColumn()
    updated_at: Date
}
