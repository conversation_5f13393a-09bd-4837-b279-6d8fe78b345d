import { Processor, Process, OnQueueActive, OnQueueCompleted, OnQueueFailed } from '@nestjs/bull';
import { Job } from 'bull';
import { decrypted } from 'common/util/utils';
import configuration from 'config/configuration';
import { BinanceHelper } from 'common/helper/binance';
import { CreateOrderDto } from 'src/order/dto/create-order.dto';
import { Api } from 'src/api/entities/api.entity';
import { Matching, MatchingStatus } from 'src/matching/entities/matching.entity';
import { EntityManager, Equal } from 'typeorm';
import { Pair } from 'src/pair/entities/pair.entity';
import { OrderSide } from 'common/interface/order.enum';
import { WalletService } from 'src/wallet/wallet.service';
import Decimal from 'decimal.js';
import { SocketGateway } from 'src/socket/socket.gateway';
import { PinoLogger } from 'nestjs-pino';

type CreateOrderJob = {
  createOrderDto: CreateOrderDto
  pair: Pair
  api: Api
  userId: number
}

@Processor('order-queue')
export class OrderQueueService {
  constructor(
    private readonly em: EntityManager,
    private readonly walletService: WalletService,
    private readonly socketGateway: SocketGateway,
    private readonly pinoLogger: PinoLogger,
  ) {
    this.pinoLogger.setContext(OrderQueueService.name)
    this.pinoLogger.info('OrderQueueService is initialized and ready.')
  }

  @Process({ name: 'create-order', concurrency: 3 })
  async handleOrder(job: Job<CreateOrderJob>) {
    try {
      const { createOrderDto, pair, api, userId } = job.data

      const secret = await decrypted(api.secret, configuration().encryption.secret)
      const binance = new BinanceHelper(api.api, secret)

      const orderDetails = await binance.newOrder(
        pair.name,
        createOrderDto.side,
        createOrderDto.type,
        Number(createOrderDto.qty),
        Number(createOrderDto.price),
        createOrderDto.strategy,
        userId,
        createOrderDto.signal_id,
      )

      if (createOrderDto.client_order_id) {
        let matchingRecord = await this.em.findOneBy(Matching, {
          order_id: Equal(createOrderDto.client_order_id),
          status: Equal(MatchingStatus.MATCHING),
        })

        if (!matchingRecord) {
          this.em.save(Matching, {
            order_id: createOrderDto.client_order_id,
            match_id: orderDetails.clientOrderId,
            status: MatchingStatus.MATCHING,
          })
        } else {
          matchingRecord.match_id = orderDetails.clientOrderId
          this.em.save(matchingRecord)
        }
      }

      return {
        success: true,
        orderDetails,
        processedAt: new Date(),
      }
    } catch (err) {
      // Only retry if the error is related to the order immediately matching
      const retryMessage = 'Order would immediately match and take'
      if (!err.message.includes(retryMessage)) {
        job.attemptsMade = 4
      }
      throw err
    }
  }

  @OnQueueActive()
  onActive(job: Job<CreateOrderJob>) {
    const { userId, pair, createOrderDto } = job.data
    this.pinoLogger.info(`[ACTIVE] Job #${job.id} - Placing ${createOrderDto.side} order for ${pair.name}, user: ${userId}, qty: ${createOrderDto.qty}`)
  }

  @OnQueueCompleted()
  onCompleted(job: Job<CreateOrderJob>, result: any) {
    const { userId, pair, createOrderDto } = job.data
    this.pinoLogger.info(
      `[COMPLETED] Job #${job.id} - ${createOrderDto.side} order for ${pair.name} completed for user ${userId}. Binance Client Order ID: ${result?.orderDetails?.clientOrderId || 'N/A'}`
    )
  }

  @OnQueueFailed()
  async onFailed(job: Job<CreateOrderJob>, err: Error) {
    const { createOrderDto, pair, api, userId } = job.data
    this.pinoLogger.error(
      `[FAILED] Job #${job.id} - Failed to place ${createOrderDto.side} order for ${pair.name}, user: ${userId}. Reason: ${err.message}`
    )

    try {
      const [spendCoin, qty] = createOrderDto.side === OrderSide.BUY ?
        [pair.quote, Decimal(createOrderDto.qty).mul(Decimal(createOrderDto.price))] :
        [pair.base, Decimal(createOrderDto.qty)]

      if (job.attemptsMade === 5) {
        await this.walletService.unfreezeFunds(
          userId,
          api.platform.id,
          createOrderDto.signal_id,
          spendCoin,
          qty,
          err.message,
        )

        this.socketGateway.server.to(`user-${userId}`).emit('order:failed', {
          order: createOrderDto,
          reason: err.message,
        })
      }
    } catch (unlockErr) {
      this.pinoLogger.error(`[UNLOCK FAILED] Failed to unlock funds for job ${job.id}: ${unlockErr.message}`)
    }
  }
}
