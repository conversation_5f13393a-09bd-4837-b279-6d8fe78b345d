import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { BinanceHelper } from 'common/helper/binance';
import { getRedis, insertRedisWithoutExpired } from 'common/helper/redis';
import { decrypted } from 'common/util/utils';
import configuration from 'config/configuration';
import Decimal from 'decimal.js';
import { PinoLogger } from 'nestjs-pino';
import { Api } from 'src/api/entities/api.entity';
import { EntityManager, Equal, FindOptionsWhere, IsNull } from 'typeorm';
import { Coin } from './entities/coin.entity';

@Injectable()
export class CoinService {
    constructor(
        private readonly em: EntityManager,
        private readonly pinoLogger: PinoLogger,
    ) {
        this.pinoLogger.setContext(CoinService.name)
    }

    private allowedCoins: Set<string> | null = null
    private readonly MAX_RATE_LIMIT = 600           // Max API weight allowed per cron cycle (10% of Binance 1m IP limit)
    private readonly GET_SPOT_BALANCE_WEIGHT = 5    // Weight for getSpotBalance API call
    private readonly API_BATCH = 1000               // Number of APIs to process per batch
    private readonly TIMEOUT_MS = 60_000            // Timeout per cron iteration (1 minute)

    // Filter for active main APIs tied to Binance platform
    private readonly FILTERS: FindOptionsWhere<Api> = {
        main_user_id: IsNull(),
        is_active: Equal(true),
        platform: { id: Equal(1) }
    }

    // Redis cursor key to track pagination progress
    private readonly CURSOR_KEY = 'staking:userCursor'

    private async loadAllowedCoins(): Promise<Set<string>> {
        if (this.allowedCoins) return this.allowedCoins

        const coins = await this.em.find(Coin, {
            where: {
                is_active: Equal(true),
            },
            select: ['symbol'],
        })
        this.allowedCoins = new Set(coins.map(c => c.symbol))

        return this.allowedCoins
    }

    /**
     * Cron job: runs every 5 minutes to trigger automated staking.
     * Allows up to 5 attempts (1 minute each), exits early if finished.
     */
    @Cron(CronExpression.EVERY_5_MINUTES, {
        name: 'autoStakeFlexi',
        waitForCompletion: true
    })
    async autoStakingScheduler() {
        this.pinoLogger.info('Cron job running: autoStakeFlexi')
        const startTime = Date.now()
        const allowedCoins = await this.loadAllowedCoins()

        for (let i = 0; i < 5; i++) {
            this.pinoLogger.debug(`Attempt ${i} running...`)

            try {
                // Run staking with timeout protection
                const isFinished = await Promise.race([
                    this.autoStaking(allowedCoins),
                    new Promise<boolean>((_, reject) =>
                        setTimeout(() => reject(new Error('Timeout')), this.TIMEOUT_MS)
                    )
                ])

                if (isFinished) {
                    this.pinoLogger.info('Cron job finished: autoStakeFlexi')
                    break
                }
            } catch (err) {
                if (err.message === 'Timeout') {
                    this.pinoLogger.info(`Attempt ${i + 1} timed out after 1 minute, starting new loop`)
                } else {
                    this.pinoLogger.error(`Error in attempt ${i + 1}:`, err.message)
                }
            }

            const elapsed = Date.now() - startTime
            const remainingTime = this.TIMEOUT_MS - elapsed

            // Wait for the remaining time (if any) before retry
            if (i < 4 && remainingTime > 0) {
                this.pinoLogger.debug(
                    `Waiting ${Math.round(remainingTime / 1000)} seconds before next attempt`
                )
                await new Promise(resolve => setTimeout(resolve, remainingTime))
            }
        }
    }

    /**
     * Main staking logic:
     * - Fetches active main API records in batches
     * - Fetches spot balances from Binance
     * - Stakes 'free' assets while respecting rate limits
     * - Persists cursor for continuation in next cron run
     */
    async autoStaking(allowedCoins: Set<string>) {
        let usedLimit = 0
        let processedApiCount = Number(await getRedis(this.CURSOR_KEY)) || 0

        // Count total active main APIs for Binance platform
        const totalApis = await this.em.count(Api, { where: this.FILTERS })

        // Return if there is no active apis
        if (totalApis === 0) {
            await insertRedisWithoutExpired(this.CURSOR_KEY, '0')
            this.pinoLogger.debug('No active main API records found')
            return true
        }

        // Return if all users have staked
        if (processedApiCount >= totalApis) {
            await insertRedisWithoutExpired(this.CURSOR_KEY, '0')
            this.pinoLogger.debug('All APIs already processed')
            return true
        }

        // Fetch a batch of API records
        const apis = await this.em.find(Api, {
            where: this.FILTERS,
            relations: ['platform'],
            skip: processedApiCount,
            take: this.API_BATCH,
        })

        // If no APIs returned, all are likely processed — exit gracefully
        if (apis.length === 0) {
            await insertRedisWithoutExpired(this.CURSOR_KEY, '0')
            this.pinoLogger.debug('No APIs found in current batch. All processed.')
            return true
        }

        // Process each API
        for (const api of apis) {
            try {
                const secret = await decrypted(api.secret, configuration().encryption.secret);
                const binanceHelper = new BinanceHelper(api.api, secret)

                if (usedLimit + this.GET_SPOT_BALANCE_WEIGHT >= this.MAX_RATE_LIMIT) {
                    this.pinoLogger.debug(`Weight cap reached before balance check; stopping at API ${api.id}`)
                    break
                }

                usedLimit += this.GET_SPOT_BALANCE_WEIGHT
                const balance = await binanceHelper.getSpotBalance()
                // this.pinoLogger.debug(`API ${api.id} spot balance: ${JSON.stringify(balance)}`)

                processedApiCount++

                // Skip if API's account has no balance
                if (balance.length === 0) {
                    this.pinoLogger.debug(`API ${api.id}: No balance, skipping`)
                    continue
                }

                const estLimitForStakes = balance.length
                // this.pinoLogger.debug(`API ${api.id} has ${balance.length} assets, extra weight ${estLimitForStakes}`)

                // If estLimitForStakes would exceed rate limit for this cron run
                // This stops the batch early to avoid hitting Binance's API limit
                if (usedLimit + estLimitForStakes >= this.MAX_RATE_LIMIT) {
                    this.pinoLogger.warn(`Rate limit would overflow at API ${api.id}, stopping batch`)
                    break
                }


                // Try staking each coin individually — on failure, continue to next coin
                for (const coin of balance) {
                    if (Decimal(coin.free).eq(0)) continue      // skip pure zeros
                    if (!allowedCoins.has(coin.asset)) continue // skip coins that are not in DB

                    try {
                        await binanceHelper.stakeFlexi(coin.asset, Number(coin.free))
                    } catch (coinErr) {
                        this.pinoLogger.warn(
                            `Failed to stake ${coin.asset} for API ${api.id}: ${coinErr.message}`
                        )
                    }
                }

                usedLimit += estLimitForStakes
            } catch (err) {
                this.pinoLogger.error(`Error processing API ${api.id}: ${err.message}`)
            }
        }

        // Update the cursor using actual processed count
        // If all APIs done, reset cursor to 0
        const nextCursor = processedApiCount >= totalApis ? 0 : processedApiCount
        await insertRedisWithoutExpired(this.CURSOR_KEY, String(nextCursor))

        if (nextCursor === 0) {
            this.pinoLogger.debug('All APIs processed, cursor reset')
            return true // Done
        }

        return false // Not done yet, will continue in next run
    }
}
