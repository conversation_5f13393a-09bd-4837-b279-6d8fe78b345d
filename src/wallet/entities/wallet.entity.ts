import { DecimalJsTransformer } from "common/util/decimal-js-transformer";
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, Unique, UpdateDateColumn } from "typeorm";

@Entity()
@Unique(['coin', 'platform_id', 'user_id', 'signal_id'])
export class Wallet {
    @PrimaryGeneratedColumn({ type: 'bigint', unsigned: true })
    id: number

    @Column()
    coin: string

    @Column({ type: 'bigint', unsigned: true })
    platform_id: number

    @Column({ type: 'bigint', unsigned: true })
    user_id: number

    @Column({ type: 'bigint', unsigned: true})
    signal_id: number

    @Column({
        default: '0',
        type: 'decimal',
        precision: 25,
        scale: 8,
        transformer: DecimalJsTransformer,
    })
    qty: string

    @Column({
        default: '0',
        type: 'decimal',
        precision: 25,
        scale: 8,
        transformer: DecimalJsTransformer,
    })
    qty_frozen: string

    @CreateDateColumn()
    created_at: Date

    @UpdateDateColumn()
    updated_at: Date
}
