import { Platform } from 'src/platform/entities/platform.entity';
import { MarketType } from 'common/interface/market-type.enum';
import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, JoinColumn, ManyToOne, OneToMany } from 'typeorm';
import { PairStatus } from 'common/interface/pair-status.enum';
import { UserPair } from 'src/user_pair/entities/user_pair.entity';
import { Order } from 'src/order/entities/order.entity';
import { Summary } from 'src/summary/entities/summary.entity';
import { DecimalJsTransformer } from 'common/util/decimal-js-transformer';

@Entity()
export class Pair {
  @PrimaryGeneratedColumn({ unsigned: true })
  id: number;

  @ManyToOne(() => Platform, { nullable: false })
  @JoinColumn({ name: 'platform_id' })
  platform: Platform;

  @Column()
  name: string;

  @Column({ type: 'enum', enum: MarketType, default: MarketType.SPOT })
  market: MarketType;

  @Column()
  base: string;

  @Column()
  quote: string;

  @Column({ default: 0 })
  price_dp: number;

  @Column({ default: 0 })
  volume_dp: number;

  @Column({ type: 'enum', enum: PairStatus, default: PairStatus.ACTIVE })
  status: PairStatus;

  @Column({
    default: '0',
    type: 'decimal',
    precision: 25,
    scale: 8,
    transformer: DecimalJsTransformer,
  })
  default_step: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @OneToMany(() => UserPair, (userPair) => userPair.pair)
  userPairs: UserPair[];
}
