import { <PERSON><PERSON><PERSON>ogger } from "nestjs-pino";
import { Platform } from "src/platform/entities/platform.entity";
import { DataSource } from "typeorm";

export async function seederPlatform(dataSource: DataSource) {
    const pinoLogger = new PinoLogger({ pinoHttp: {} })
    const repo = dataSource.getRepository(Platform)
    const count = await repo.count()

    pinoLogger.setContext(seederPlatform.name)

    if (count === 0) {
        await repo.save([
            { name: "Binance" },
            { name: "ByBit" },
            { name: "<PERSON><PERSON>" },
            { name: "HataWW" }
        ])
        pinoLogger.info('[SEED] Platform table seeded.')
    }
}