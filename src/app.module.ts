import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { QueueModule } from './queue/queue.module';
import { AuthModule } from './auth/auth.module';
import { ThrottlerGuard, ThrottlerModule } from '@nestjs/throttler';
import { APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core';
import { AdminModule } from './admin/admin.module';
import { UserModule } from './user/user.module';
import { ActivityLogInterceptor } from 'common/interceptor/activity-log.interceptor';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ActivityLog } from 'common/entity/activity-log.entity';
import { Admin } from './admin/entities/admin.entity';
import { User } from './user/entities/user.entity';
import configuration from 'config/configuration';
import { MailerModule } from '@nestjs-modules/mailer';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
import { ApiModule } from './api/api.module';
import { PlatformModule } from './platform/platform.module';
import { Platform } from './platform/entities/platform.entity';
import { Api } from './api/entities/api.entity';
import { SignalModule } from './signal/signal.module';
import { Signal } from './signal/entities/signal.entity';
import { News } from './news/entities/news.entity';
import { NewsModule } from './news/news.module';
import { UserPairModule } from './user_pair/user_pair.module';
import { UserPair } from './user_pair/entities/user_pair.entity';
import { OrderFeeModule } from './order_fee/order_fee.module';
import { DepositModule } from './deposit/deposit.module';
import { OrderFee } from './order_fee/entities/order_fee.entity';
import { Deposit } from './deposit/entities/deposit.entity';
import { OrderModule } from './order/order.module';
import { PairModule } from './pair/pair.module';
import { Order } from './order/entities/order.entity';
import { Pair } from './pair/entities/pair.entity';
import { ScheduleModule } from '@nestjs/schedule';
import { MatchingModule } from './matching/matching.module';
import { Matching } from './matching/entities/matching.entity';
import { ProfitModule } from './profit/profit.module';
import { Profit } from './profit/entities/profit.entity';
import { BlacklistedToken } from 'common/entity/blacklisted-token.entity';
import { SummariesModule } from './summary/summary.module';
import { DatastreamModule } from './datastream/datastream.module';
import { WalletModule } from './wallet/wallet.module';
import { CoinModule } from './coin/coin.module';
import { Coin } from './coin/entities/coin.entity';
import { Wallet } from './wallet/entities/wallet.entity';
import { TransactionLog } from 'common/entity/transaction-log.entity';
import { SocketModule } from './socket/socket.module';
import { LoggerModule } from 'nestjs-pino';
import { randomUUID } from 'crypto';

@Module({
  imports: [
    MailerModule.forRoot({
      transport: {
        host: configuration().email.host,
        port: configuration().email.port,
        secure: configuration().email.port == 465 ? true : false, // true for 465, false for other ports
        auth: {
          user: configuration().email.user, // generated ethereal user
          pass: configuration().email.password, // generated ethereal password
        },
      },
      defaults: {
        from: `"Support" <${configuration().email.user}>`, // outgoing email ID
      },
      template: {
        dir: process.cwd() + '/src/template/',
        adapter: new HandlebarsAdapter(), // or new PugAdapter() or new EjsAdapter()
        options: { strict: true },
      },
    }),
    ScheduleModule.forRoot(),
    QueueModule,
    AuthModule,
    AdminModule,
    UserModule,
    ApiModule,
    PlatformModule,
    NewsModule,
    SignalModule,
    PairModule,
    UserPairModule,
    DepositModule,
    OrderFeeModule,
    DepositModule,
    OrderModule,
    PairModule,
    MatchingModule,
    ProfitModule,
    SummariesModule,
    DatastreamModule,
    WalletModule,
    CoinModule,
    SocketModule,
    TypeOrmModule.forRootAsync({
      useFactory: async () => ({
        type: 'mysql',
        host: configuration().database.host,
        port: configuration().database.port,
        username: configuration().database.username,
        password: configuration().database.password,
        database: configuration().database.database,
        entities: ['dist/**/*.entity{.ts,.js}'],
        synchronize: configuration().database.synchronize,
        // logging: true,
      }),
    }),
    ThrottlerModule.forRoot([{ ttl: 1000, limit: 10 }]),
    TypeOrmModule.forFeature([ActivityLog, Admin, User, Platform, Api, Signal, News, Pair, UserPair, Order, Deposit, OrderFee, Matching, Profit, BlacklistedToken, Wallet, Coin, TransactionLog]),
    LoggerModule.forRoot({
      pinoHttp: {
        level: configuration().environment === 'PRODUCTION' ? 'info' : 'debug',
        genReqId: (req) => {
          return randomUUID();
        },
        serializers: {
          req(req) {
            return {
              method: req.method,
              url: req.url,
              query: req.query, // Optional
            };
          },
          res(res) {
            return {
              statusCode: res.statusCode,
            };
          },
        },

        customProps: (req) => {
          const { user, id } = req as unknown as {
            user: { id?: string; address?: string };
            id: string;
          };
          return {
            userId: user?.id,
            userAddress: user?.address,
            traceId: id,
          };
        },

        customSuccessMessage(req, res) {
          return `${res.statusCode} ${req.method} ${req.url}`;
        },
        customErrorMessage(req, res, err) {
          return `${res.statusCode} ${req.method} ${req.url} - ${err.message}`;
        },

        transport: configuration().environment === 'PRODUCTION' ? {
          target: '@logtail/pino',
          options: {
            sourceToken: configuration().taillog.token,
            options: {
              endpoint: `https://${configuration().taillog.url}`,
            },
          },
        } : {
          target: 'pino-pretty',
          options: {
            singleLine: true
          },
          level: 'info'
        },
      },
      exclude: ['status'],
    }),
  ],
  controllers: [AppController],
  providers: [
    AppService,
    { provide: APP_GUARD, useClass: ThrottlerGuard },
    { provide: APP_INTERCEPTOR, useClass: ActivityLogInterceptor },
  ],
})
export class AppModule { }
