import { Injectable, OnModuleInit } from '@nestjs/common';
import { OrderSide, OrderType } from 'common/interface/order.enum';
import Decimal from 'decimal.js';
import { PinoLogger } from 'nestjs-pino';
import { Api } from 'src/api/entities/api.entity';
import { Matching, MatchingStatus } from 'src/matching/entities/matching.entity';
import { Order, OrderStatus } from 'src/order/entities/order.entity';
import { Pair } from 'src/pair/entities/pair.entity';
import { Platform } from 'src/platform/entities/platform.entity';
import { Signal } from 'src/signal/entities/signal.entity';
import { SocketGateway } from 'src/socket/socket.gateway';
import { User } from 'src/user/entities/user.entity';
import { WalletService } from 'src/wallet/wallet.service';
import { EntityManager, Equal } from 'typeorm';
import * as WebSocket from 'ws';

@Injectable()
export class DatastreamBinanceService implements OnModuleInit {
  private socket: WebSocket;

  constructor(
    private readonly em: EntityManager,
    private readonly walletService: WalletService,
    private readonly socketGateway: SocketGateway,
    private readonly pinoLogger: PinoLogger,
  ) {
    this.pinoLogger.setContext(DatastreamBinanceService.name)
  }

  onModuleInit() {
    this.binanceSpotUserStream()
  }

  private async getActiveListenKeys(): Promise<string[]> {
    const keys = await this.em.createQueryBuilder(Api, 'a')
      .where('a.is_active = :is_active', { is_active: true })
      .andWhere('a.platform_id = :platform_id', { platform_id: 1 })
      .andWhere('a.main_user_id IS NULL')
      .getMany()

    return keys
      .filter(k => k.listen_key)
      .map(k => k.listen_key!) // Safe because of filter
  }

  async binanceSpotUserStream() {
    const activeKeys = await this.getActiveListenKeys()

    if (activeKeys.length === 0) {
      this.pinoLogger.warn("No active listen keys found")
      return
    }

    this.pinoLogger.info(`Creating datastream with ${activeKeys.length} active listen keys`)

    const streamPath = activeKeys.join('/')
    await this.listenUserStream(streamPath)
  }

  async listenUserStream(listenKeys: string) {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      this.socket.close()
      this.pinoLogger.info("Closed previous WebSocket")
    }

    const url = `wss://stream.binance.com:9443/stream?streams=${listenKeys}`
    this.socket = new WebSocket.WebSocket(url)

    this.socket.on('open', () => {
      this.pinoLogger.info('Datastream socket connection established');
    })

    this.socket.on('message', async (rawData) => {
      try {
        const { data } = JSON.parse(rawData.toString())
        if (data.e === 'executionReport') {
          await this.handleExecutionReport(data)
        }
      } catch (err) {
        this.pinoLogger.error("Error handling WebSocket message:", err.message)
      }
    })
  }

  private async handleExecutionReport(data: any) {
    // this.pinoLogger.debug(data)

    const clientOrderId = data.C || data.c || ''
    const match = clientOrderId.match(/^([A-Z]+)-(\d+)-(\d+)-(\d{13})$/)
    if (!match) {
      this.pinoLogger.warn(`Skipped invalid clientOrderId format: ${clientOrderId}`)
      return
    }

    // Skip initial NEW status for MARKET orders
    if (data.o === OrderType.MARKET && data.X === 'NEW') return

    const [strategy, signalIdStr, userIdStr] = clientOrderId.split('-')
    const userId = parseInt(userIdStr)
    const signalId = parseInt(signalIdStr)

    const [user, pair] = await Promise.all([
      this.em.findOneBy(User, { id: Equal(userId) }),
      this.em.findOneBy(Pair, { name: Equal(data.s), platform: { id: Equal(1) } }),
    ])
    if (!user || !pair) return

    const isBuy = data.S === OrderSide.BUY
    const [spendCoin, receiveCoin] = isBuy ? [pair.quote, pair.base] : [pair.base, pair.quote]

    // Quantity and pricing details
    const baseQty = Decimal(data.q)               // Original base qty
    const quoteQty = Decimal(data.p).mul(baseQty) // Estimated quote qty
    const filledBaseQty = Decimal(data.z)         // Actual base filled
    const filledQuoteQty = Decimal(data.Z)        // Actual quote filled
    const price = filledBaseQty.gt(0) ? filledQuoteQty.div(filledBaseQty) : Decimal(data.p)
    const status = this.getOrderStatus(data.X, filledBaseQty)

    if (status === OrderStatus.CANCELLED) {
      const unlockQty = isBuy ? quoteQty : baseQty
      await this.handleCancelledOrder(clientOrderId)
      await this.walletService.unfreezeFunds(user.id, 1, signalId, spendCoin, unlockQty, `${status} ${data.S} ${data.s}`, clientOrderId)
    }

    if (status === OrderStatus.OPEN) {
      const frozenQty = isBuy ? quoteQty : baseQty
      const spentQty = isBuy ? filledQuoteQty : filledBaseQty
      const receivedQty = isBuy ? filledBaseQty : filledQuoteQty

      await this.walletService.handlePostTrade(
        user.id,
        1,
        signalId,
        spendCoin,
        receiveCoin,
        frozenQty,
        spentQty,
        receivedQty,
        clientOrderId
      )
    }

    // Prepare order update
    const updateData: Partial<Order> = {
      qty: baseQty.toString(),
      filled: filledBaseQty.toString(),
      price: price.toString(),
      status,
      client_order_id: clientOrderId,
      amount: filledQuoteQty.toString(),
    }

    if (signalId !== 0) {
      updateData.signal = await this.em.findOneBy(Signal, { id: Equal(signalId) })
    }

    // Fetch or create order record
    let order = await this.em.findOne(Order, {
      where: { client_order_id: Equal(clientOrderId) },
      relations: ['user', 'signal', 'platform'],
    })

    if (!order) {
      order = new Order()
      order.order_id = data.i
      order.user = user
      order.pair_name = data.s
      order.is_buy = isBuy
      order.strategy = strategy
      order.platform = await this.em.findOneBy(Platform, { id: Equal(1) })
    }

    Object.assign(order, updateData)
    await this.em.save(order)

    this.socketGateway.server.to(`user-${userId}`).emit('order:updated', order)
  }

  private getOrderStatus(status: string, filledBaseQty: Decimal): OrderStatus {
    if (status === 'FILLED' || (filledBaseQty.gt(0) && status === 'CANCELED')) return OrderStatus.OPEN
    if ((filledBaseQty.eq(0) && status === 'CANCELED') || status === 'EXPIRED') return OrderStatus.CANCELLED
    if (status === 'PARTIALLY_FILLED' || status === 'NEW') return OrderStatus.NEW
    this.pinoLogger.warn(`Unknown order status: ${status}`)
  }

  private async handleCancelledOrder(clientOrderId: string) {
    const matchingRecord = await this.em.findOneBy(Matching, {
      match_id: Equal(clientOrderId),
      status: Equal(MatchingStatus.MATCHING),
    })

    if (!matchingRecord) return

    matchingRecord.match_id = null
    await this.em.save(matchingRecord)
  }
}
