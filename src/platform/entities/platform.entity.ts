import { Api } from 'src/api/entities/api.entity';
import { Order } from 'src/order/entities/order.entity';
import { Pair } from 'src/pair/entities/pair.entity';
import { Deposit } from 'src/deposit/entities/deposit.entity';
import {
    Entity,
    Column,
    PrimaryGeneratedColumn,
    CreateDateColumn,
    UpdateDateColumn,
    OneToMany,
} from 'typeorm';

@Entity()
export class Platform {
    @PrimaryGeneratedColumn({ unsigned: true })
    id: number;

    @Column({ unique: true })
    name: string;

    @Column({ default: 1 })
    is_active: boolean;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @OneToMany(() => Api, (api) => api.platform)
    apis: Api[]

    @OneToMany(() => Pair, (pair) => pair.platform)
    pairs: Pair[]

    @OneToMany(() => Deposit, (deposit) => deposit.platform)
    deposits: Deposit[]

    @OneToMany(() => Order, (order) => order.platform)
    orders: Order[]
}
