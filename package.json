{"name": "ezbot", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@binance/simple-earn": "^5.0.2", "@binance/spot": "^6.0.0", "@binance/wallet": "^5.0.0", "@logtail/pino": "^0.5.5", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^4.0.0", "@nestjs/bull": "^11.0.2", "@nestjs/bullmq": "^11.0.2", "@nestjs/common": "^11.1.0", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.0", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "*", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.0", "@nestjs/platform-socket.io": "^11.1.3", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.1.6", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^11.1.3", "axios": "^1.9.0", "bcrypt": "^5.1.1", "bull": "^4.16.5", "bullmq": "^5.53.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cookie-parser": "^1.4.7", "decimal.js": "^10.5.0", "dotenv": "^16.5.0", "hi-base32": "^0.5.1", "ioredis": "^5.6.1", "moment-timezone": "^0.5.48", "mysql2": "^3.14.1", "nestjs-pino": "^4.4.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pino-http": "^10.5.0", "pino-pretty": "^13.0.0", "redis": "^4.7.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "socket.io": "^4.8.1", "typeorm": "^0.3.22"}, "devDependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.0", "@types/express": "^5.0.1", "@types/jest": "^29.5.14", "@types/node": "^22.15.3", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "jest": "^29.7.0", "prettier": "^3.5.3", "source-map-support": "^0.5.21", "supertest": "^7.1.0", "ts-jest": "^29.3.2", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}