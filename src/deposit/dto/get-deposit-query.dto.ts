import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsInt, IsN<PERSON>ber, IsOptional, IsString, Min } from "class-validator";
import { Type } from "class-transformer";

export class GetDepositQueryDto {
    @ApiPropertyOptional({ description: 'Deposit coin' })
    @IsOptional()
    @IsString()
    coin?: string

    @ApiPropertyOptional({ description: 'Platform ID' })
    @IsOptional()
    @Type(() => Number)
    @Min(1)
    @IsInt()
    platform_id?: number

    @ApiPropertyOptional({ description: 'User ID' })
    @IsOptional()
    @Type(() => Number)
    @Min(1)
    @IsInt()
    user_id?: number;

    @ApiPropertyOptional({ description: 'Deposit ID' })
    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    deposit_id?: number;
}