import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { HttpExceptionFilter } from 'common/filter/http-exception.filter';
import { ResponseInterceptor } from 'common/interceptor/response.interceptor';
import { GlobalValidationPipes } from 'common/pipe/validation.pipe';
import { ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as cookieParser from 'cookie-parser';
import * as bodyParser from 'body-parser';
import {
  JsendObjectError,
  JsendObjectSuccess,
} from 'common/interface/jsend-interface';
import { PageDto } from 'common/dto/pageResponse.dto';
import { PageOptionsDto } from 'common/dto/pagination.dto';
import configuration from 'config/configuration';
import { ActivityLogInterceptor } from 'common/interceptor/activity-log.interceptor';
import { DataSource } from 'typeorm';
import { seederPlatform } from 'common/seeder/platform.seed';
import { seederPair } from 'common/seeder/pair.seed';
import { seederCoin } from 'common/seeder/coin.seed';
import { seederSignal } from 'common/seeder/signal.seed';
import { Logger } from 'nestjs-pino';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, { logger: false });
  app.useLogger(app.get(Logger));
  app.enableCors({ credentials: true, origin: true });
  app.useGlobalFilters(new HttpExceptionFilter());
  app.useGlobalInterceptors(new ResponseInterceptor());
  app.useGlobalPipes(
    new GlobalValidationPipes(),
    new ValidationPipe({ transform: true }),
  );
  const option = new DocumentBuilder()
    .setTitle(`Api Doc`)
    .setVersion('1.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'Authorization',
        in: 'header',
      },
      'access-token',
    )
    .build();
  // Set global no-cache headers
  app.use((req, res, next) => {
    if (req.path.includes('/api')) {
      res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      res.setHeader('Surrogate-Control', 'no-store');
    }
    next();
  });
  app.use(cookieParser());
  app.use(bodyParser.json({ limit: '20mb' }));
  app.use(bodyParser.urlencoded({ limit: '20mb', extended: true }));
  const document = SwaggerModule.createDocument(app, option, {
    extraModels: [
      JsendObjectSuccess,
      JsendObjectError,
      PageDto,
      PageOptionsDto,
    ],
  });
  SwaggerModule.setup('api', app, document, {
    swaggerOptions: { defaultModelsExpandDepth: -1 },
  });

  const dataSource = app.get(DataSource)
  await seederPlatform(dataSource)
  await seederPair(dataSource)
  await seederCoin(dataSource)
  await seederSignal(dataSource)

  await app.listen(configuration().port, '0.0.0.0');
}
bootstrap();
