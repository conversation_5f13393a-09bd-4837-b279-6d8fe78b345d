import { Strategy, ExtractJwt } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import {
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { EntityManager, Equal } from 'typeorm';
import configuration from 'config/configuration';
import { Admin } from 'src/admin/entities/admin.entity';
import { BlacklistedToken } from 'common/entity/blacklisted-token.entity';

@Injectable()
export class JwtAdminStrategy extends PassportStrategy(
  Strategy,
  'jwt-admin',
) {
  constructor(private readonly em: EntityManager) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configuration().jwtConstant.jwtAccessSecret,
      passReqToCallback: true,
    });
  }

  async validate(req: Request, payload: any) {
    try {

      const accessToken = req.headers['authorization']?.replace('Bearer ', '');
      const refreshToken = req.headers['refreshtoken']?.replace('Bearer ', '');

      const isBlacklisted = await this.em
        .createQueryBuilder(BlacklistedToken, 'b')
        .where('b.access_token = :accessToken OR b.refresh_token = :refreshToken', {
          accessToken,
          refreshToken,
        })
        .getOne();

      if (isBlacklisted) {
        throw new UnauthorizedException('Access Token and Refresh Token is blacklisted')
      }

      const admin = await this.em.findOneBy(Admin, {
        email: Equal(payload.admin.email),
      });

      if (!admin) {
        throw new UnauthorizedException('Admin not found')
      }

      return { admin: admin }
    } catch (error) {
      throw new UnauthorizedException(error.message);
    }
  } 
}
