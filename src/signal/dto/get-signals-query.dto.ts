import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsBoolean, IsEnum, IsOptional } from "class-validator";
import { ToBoolean } from "common/decorator/to-boolean.decorator";
import { MarketType } from "common/interface/market-type.enum";

export class GetSignalsQueryDto {
    @ApiPropertyOptional({ description: 'signal market type' })
    @IsOptional()
    @IsEnum(MarketType)
    market?: MarketType;

    @ApiPropertyOptional({ description: 'signal active status' })
    @IsOptional()
    @ToBoolean()
    @IsBoolean()
    is_active?: Boolean;
}
