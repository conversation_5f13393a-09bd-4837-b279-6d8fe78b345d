import { Module } from '@nestjs/common';
import { SocketGateway } from './socket.gateway';
import { JwtModule } from '@nestjs/jwt';
import configuration from 'config/configuration';

@Module({
    imports: [
        JwtModule.register({
            secret: configuration().jwtConstant.jwtAccessSecret,
            signOptions: { expiresIn: configuration().jwtConstant.jwtAccessExpired },
        }),
    ],
    providers: [SocketGateway],
    exports: [SocketGateway],
})
export class SocketModule { }
