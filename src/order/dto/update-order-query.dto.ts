import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsInt, IsNumberString, IsOptional, IsString, Min } from 'class-validator';
import { OrderStatus } from '../entities/order.entity';
import { Type } from 'class-transformer';
import { ToBoolean } from 'common/decorator/to-boolean.decorator';

export class UpdateOrderDto {
    @ApiPropertyOptional({ description: 'order id' })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    order_id: number;

    @ApiPropertyOptional({ description: 'pair id' })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    pair_id: number;

    @ApiPropertyOptional({ description: 'platform id' })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    platform_id: number;

    @ApiPropertyOptional({ description: 'user id' })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    user_id: number;

    @ApiPropertyOptional({ description: 'signal id' })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    @Min(1)
    signal_id: number;

    @ApiPropertyOptional({ description: 'Client Order Id' })
    @IsOptional()
    @IsString()
    client_order_id: string

    @ApiPropertyOptional({ description: 'Pair Name (Not required pass in)' })
    @IsOptional()
    @IsString()
    pair_name: string;

    @ApiPropertyOptional({ description: 'order price' })
    @IsOptional()
    @IsNumberString()
    price: string

    @ApiPropertyOptional({ description: 'Quantity of pair in order' })
    @IsOptional()
    @IsNumberString()
    qty: string;

    @ApiPropertyOptional({ description: 'Filled of pair in order' })
    @IsOptional()
    @IsNumberString()
    filled: string;

    @ApiPropertyOptional({ description: 'Matched of pair in order' })
    @IsOptional()
    @IsNumberString()
    matched: string;

    @ApiPropertyOptional({ description: 'order amount' })
    @IsOptional()
    @IsNumberString()
    amount: string

    @ApiPropertyOptional({ description: 'order status' })
    @IsOptional()
    @IsEnum(OrderStatus)
    status: OrderStatus

    @ApiPropertyOptional({ description: 'order long or short' })
    @IsOptional()
    @ToBoolean()
    @IsBoolean()
    is_buy: boolean

    @ApiPropertyOptional({ description: 'order fees' })
    @IsOptional()
    @ToBoolean()
    @IsBoolean()
    fee_done: boolean
}
