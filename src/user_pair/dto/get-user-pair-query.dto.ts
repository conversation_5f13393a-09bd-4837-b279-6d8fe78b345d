import { ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsBoolean, IsInt, IsOptional, Min } from "class-validator";
import { ToBoolean } from "common/decorator/to-boolean.decorator";

export class GetUserPairQueryDto {
    @ApiPropertyOptional({ description: 'Platform ID', example: '1' })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    @Min(1)
    platform_id?: number;

    @ApiPropertyOptional({ description: 'Signal ID', example: '1' })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    @Min(1)
    signal_id?: number;

    @ApiPropertyOptional({ description: 'User pair active status', example: 'true' })
    @IsOptional()
    @ToBoolean()
    @IsBoolean()
    is_active?: boolean;
}