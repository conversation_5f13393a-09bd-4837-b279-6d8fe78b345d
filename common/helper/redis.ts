import { createClient, RedisClientType } from 'redis';
import configuration from 'config/configuration';
import { <PERSON><PERSON><PERSON>ogger } from 'nestjs-pino';

let client: RedisClientType;

export async function getRedisClient() {
  if (!client) {
    const { redis } = configuration();

    client = createClient({
      url: `redis://${redis.host}:${redis.port}`,
      password: redis.password,
    });

    client.on('error', (err) => console.error('Redis Client Error', err));
    await client.connect();
  }

  return client;
}

async function insertRedis(key: string, value: string, expireSeconds: number) {
  client = await getRedisClient();
  await client.SET(key, value, {
    EX: expireSeconds,
  });
}

async function insertRedisWithoutExpired(key: string, value: string) {
  client = await getRedisClient();
  await client.SET(key, value);
}

async function getRedis(key: string) {
  client = await getRedisClient();
  if (!key) {
    console.error("Invalid key for retrieve");
    return;
  }

  let value = await client.GET(key);
  return value;
}

async function delRedis(key: string) {
  client = await getRedisClient();
  let value = await client.DEL(key);
  return value;
}

async function incrementWithExpiry(key: string, weight: number, ttlSeconds: number): Promise<number> {
  const client = await getRedisClient();
  const tx = client.multi();
  tx.incrBy(key, weight);
  tx.expire(key, ttlSeconds);
  const results = await tx.exec();

  const current = results ? results[0] : 0;
  return Number(current);
}

export { insertRedis, getRedis, delRedis, insertRedisWithoutExpired, incrementWithExpiry };
