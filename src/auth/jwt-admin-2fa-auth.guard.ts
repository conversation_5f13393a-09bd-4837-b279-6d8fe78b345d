import { ExecutionContext, HttpException, Injectable } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

@Injectable()
export class JwtAdmin2FAAuthGuard extends AuthGuard(
  'your-own-admin-cookie-2fa',
) {
  constructor() {
    super();
  }
  canActivate(context: ExecutionContext) {
    return super.canActivate(context);
  }

  handleRequest(err, user, info) {
    // You can throw an exception based on either "info" or "err" arguments
    if (err) {
      throw new HttpException(err.message, 400);
    } else if (!user) {
      throw new HttpException(
        'Must call login before proceed to 2fa checking',
        401,
      );
    }
    return user;
  }
}
