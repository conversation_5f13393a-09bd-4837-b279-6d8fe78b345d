import { JwtService } from '@nestjs/jwt';
import {
    WebSocketGateway,
    WebSocketServer,
    OnGatewayDisconnect,
    OnGatewayConnection,
} from '@nestjs/websockets';
import configuration from 'config/configuration';
import { <PERSON><PERSON><PERSON>ogger } from 'nestjs-pino';
import { Server, Socket } from 'socket.io';
import { User } from 'src/user/entities/user.entity';
import { EntityManager, Equal } from 'typeorm';

@WebSocketGateway(Number(process.env.SOCKET_PORT), {
    host: '127.0.0.1',
    cors: {
        origin: [
            'http://localhost:3000',
            'http://localhost:5173',
        ],
        // methods: ['GET', 'POST'],
        // allowedHeaders: ['Content-Type', 'Authorization'],
        // credentials: true,
    },
    path: '/socket.io',
    transports: ['websocket', 'polling'],
    namespace: 'ws',
    pingInterval: 25000, // 25s between pings
    pingTimeout: 5000, // disconnect if no pong received in 5s
})
export class SocketGateway implements OnGatewayConnection, OnGatewayDisconnect {
    constructor(
        private readonly em: EntityManager,
        private readonly jwtService: JwtService,
        private readonly pinoLogger: PinoLogger,
    ) {
        this.pinoLogger.setContext(SocketGateway.name)
    }

    @WebSocketServer()
    server: Server
    count: number = 0

    async handleConnection(client: Socket, ...args: any[]) {
        const token = client.handshake.headers.authorization.replace('Bearer ', '')
        if (!token) {
            this.pinoLogger.warn('❌ Socket auth failed: Missing Authorization header')
            client.disconnect()
            return
        }

        try {
            const payload = this.jwtService.verify(token, {
                secret: configuration().jwtConstant.jwtAccessSecret
            })

            const userId = payload?.user?.id
            if (!userId) {
                this.pinoLogger.warn('❌ Socket auth failed: Invalid token payload');
                client.disconnect();
                return;
            }

            const user = await this.em.findOneBy(User, { id: Equal(userId) })
            if (!user) {
                this.pinoLogger.warn('❌ Socket auth failed: User not found')
                client.disconnect()
                return
            }

            client.data.userId = userId
            // Join room based on userId
            client.join(`user-${userId}`)

            this.count++
            this.pinoLogger.info(`Client ${client.id} connected and joined room user-${userId}`)
            this.pinoLogger.info(`Total connected clients: ${this.count}`)
        } catch (err) {
            this.pinoLogger.warn(`❌ Socket auth failed: ${err.message}`)
            client.disconnect()
        }
    }

    handleDisconnect(client: Socket) {
        this.count--
        this.pinoLogger.info(`Client ${client.id} disconnected from room user-${client.data.userId}`)
        this.pinoLogger.info(`Total connected clients: ${this.count}`)
    }
}