import { Controller, Post, Body, Req, UseGuards, Get, Query } from '@nestjs/common';
import { OrderService } from './order.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { ApiBearerAuth, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/auth/jwt-auth.guard';
import { CancelOrderDto } from './dto/cancel-order.dto';
import { Throttle } from '@nestjs/throttler';
import { GetOrderQueryDto } from './dto/get-order-query.dto';
import { OrderStatus } from './entities/order.entity';

@ApiTags('Order')
@Controller('order')
export class OrderController {
  constructor(
    private readonly orderService: OrderService
  ) { }

  @Post()
  @Throttle({ default: { ttl: 1000, limit: 1 } })
  @ApiBearerAuth('access-token')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Create a new order',
    description: `Creates a new order.
  
  - \`side\` must be either **BUY** or **SELL**.
  - \`type\` must be either **MARKET** or **LIMIT_MAKER**.
  - If \`type\` is **MARKET**, \`price\` is ignored.
  - Providing \`client_order_id\` will close an order; omitting it will create a new one.
  
  **Throttle limit:** This endpoint can be called only once every **1** second.`
  })
  async queueCreateOrder(@Body() createOrderDto: CreateOrderDto, @Req() req: any) {
    return this.orderService.queueCreateOrder(createOrderDto, req.user)
  }

  @Post('cancel')
  @ApiBearerAuth('access-token')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Cancel an order',
    description: 'Cancel an order'
  })
  async cancelOrder(@Body() cancelOrderDto: CancelOrderDto, @Req() req: any) {
    return this.orderService.cancelOrder(cancelOrderDto, req.user)
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Get all orders',
    description: 'Get all orders with optional filtering by pair_name, platform_id, signal_id, and status',
  })
  @ApiQuery({
    name: 'pair_name',
    description: 'Pair name',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'platform_id',
    description: 'Platform ID',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'signal_id',
    description: 'Signal ID',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'status',
    description: 'Order status',
    required: false,
    enum: OrderStatus,
  })
  async getOrders(@Query() getOrderQueryDto: GetOrderQueryDto, @Req() req: any) {
    return await this.orderService.getOrders(getOrderQueryDto, req.user)
  }
}
