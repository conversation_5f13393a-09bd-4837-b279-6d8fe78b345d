import {
    <PERSON><PERSON>ty,
    Column,
    PrimaryGeneratedColumn,
    CreateDateColumn,
    UpdateDateColumn,
} from 'typeorm';

@Entity()
export class News {
    @PrimaryGeneratedColumn({ unsigned: true })
    id: number

    @Column({ type: 'longtext' })
    title: string

    @Column()
    source: string

    @Column('longtext')
    url: string

    @Column({ type: 'text', nullable: true })
    related: string

    @Column({ nullable: true })
    preview_img: string

    @Column('timestamp')
    published_at: Date

    @CreateDateColumn()
    created_at: string;

    @UpdateDateColumn()
    updated_at: string;
}
