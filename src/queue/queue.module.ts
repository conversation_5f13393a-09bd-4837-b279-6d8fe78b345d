import { BullModule, BullModuleOptions } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import configuration from 'config/configuration';
import { EmailQueueService } from './email-queue.service';
import { SocketModule } from 'src/socket/socket.module';
import { OrderQueueService } from './order-queue.service';
import { WalletModule } from 'src/wallet/wallet.module';

const bullConfig: BullModuleOptions = {
  redis: {
    host: configuration().redis.host,
    port: configuration().redis.port,
    password: configuration().redis.password,
  },
};

@Module({
  imports: [
    BullModule.forRoot(bullConfig),
    WalletModule,
    SocketModule,
  ],
  providers: [EmailQueueService, OrderQueueService],
})
export class QueueModule { }
