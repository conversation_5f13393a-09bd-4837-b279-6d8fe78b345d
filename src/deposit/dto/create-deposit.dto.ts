import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNotEmpty, IsNumber, IsNumberString, IsString, Min } from 'class-validator';

export class CreateDepositDto {
    @ApiProperty({ description: 'Deposit Coin' })
    @IsNotEmpty()
    @IsString()
    coin: string

    @ApiProperty({ description: 'Deposit Quantity' })
    @IsNotEmpty()
    @IsNumberString()
    qty: string

    @ApiProperty({ description: 'Deposit from Platform Id' })
    @IsNotEmpty()
    @Type(() => Number)
    @Min(1)
    @IsNumber()
    platform_id: number

    @ApiProperty({ description: 'User Id that owns deposit' })
    @IsNotEmpty()
    @Type(() => Number)
    @Min(1)
    @IsNumber()
    user_id: number;

    @ApiProperty({ description: 'Deposit Id' })
    @IsNotEmpty()
    @Type(() => Number)
    @IsNumber()
    deposit_id: number;
}
