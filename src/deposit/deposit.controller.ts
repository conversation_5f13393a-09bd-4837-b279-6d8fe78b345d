import { Controller, Get, Post, Body, Query } from '@nestjs/common';
import { DepositService } from './deposit.service';
import { CreateDepositDto } from './dto/create-deposit.dto';
import { GetDepositQueryDto } from './dto/get-deposit-query.dto';
import { ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';

@ApiTags('Deposit')
@Controller('deposit')
export class DepositController {
  constructor(
    private readonly depositService: DepositService
  ) { }

  // @Post()
  // async create(@Body() createDepositDto: CreateDepositDto) {
  //   return await this.depositService.create(createDepositDto);
  // }

  // @Get()
  // @ApiOperation({
  //   summary: 'Get all deposits',
  //   description: 'Get all deposits with optional filtering by coin, platform_id, user_id, deposit_id',
  // })
  // @ApiQuery({
  //   name: 'coin',
  //   required: false,
  //   type: String,
  //   description: 'Coin name'
  // })
  // @ApiQuery({
  //   name: 'platform_id',
  //   required: false,
  //   type: Number,
  //   description: 'Platform ID'
  // })
  // @ApiQuery({
  //   name: 'user_id',
  //   required: false,
  //   type: Number,
  //   description: 'User ID'
  // })
  // @ApiQuery({
  //   name: 'deposit_id',
  //   required: false,
  //   type: Number,
  //   description: 'Deposit ID'
  // })
  // async find(@Query() getDepositQueryDto: GetDepositQueryDto) {
  //   return await this.depositService.find(getDepositQueryDto);
  // }
}
