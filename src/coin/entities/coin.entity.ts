import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity()
export class Coin {
    @PrimaryGeneratedColumn({ type: 'bigint', unsigned: true })
    id: number

    @Column({ unique: true })
    name: string

    @Column({ unique: true })
    symbol: string

    @Column({ type: 'int' })
    vol_dp: number

    @Column({ type: 'int' })
    price_dp: number

    @Column({ default: 1 })
    is_active: boolean
}
