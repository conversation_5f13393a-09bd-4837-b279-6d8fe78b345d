import { BadRequestException, Injectable } from '@nestjs/common';
import { CreateDepositDto } from './dto/create-deposit.dto';
import { EntityManager, Equal } from 'typeorm';
import { Deposit } from './entities/deposit.entity';
import { Platform } from 'src/platform/entities/platform.entity';
import { User } from 'src/user/entities/user.entity';
import { CustomErrorMessages } from 'common/helper/errorMessage';
import Decimal from 'decimal.js';

@Injectable()
export class DepositService {
  constructor(
    private readonly em: EntityManager
  ) { }

  // async create(createDepositDto: CreateDepositDto) {
  //   const platform = await this.em.findOneBy(Platform, {
  //     id: Equal(createDepositDto.platform_id),
  //   });

  //   if (!platform)
  //     throw new BadRequestException(CustomErrorMessages.PLATFORM.INVALID_PLATFORM)

  //   const user = await this.em.findOneBy(User, {
  //     id: Equal(createDepositDto.user_id),
  //   })

  //   if (!user)
  //     throw new BadRequestException(CustomErrorMessages.USER.USER_NOT_FOUND)

  //   if (Decimal(createDepositDto.qty).lessThanOrEqualTo(0))
  //     throw new BadRequestException(CustomErrorMessages.DEPOSIT.INVALID_QTY)

  //   const deposit = new Deposit();
  //   deposit.qty = createDepositDto.qty;
  //   deposit.deposit_id = createDepositDto.deposit_id;
  //   deposit.platform = platform;
  //   deposit.coin = createDepositDto.coin;
  //   deposit.user = user

  //   await this.em.save(deposit);

  //   return 'Deposit created successfully';
  // }
}
