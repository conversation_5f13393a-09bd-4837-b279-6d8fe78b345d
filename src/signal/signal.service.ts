import { Injectable } from '@nestjs/common';
import { EntityManager } from 'typeorm';
import { Signal } from './entities/signal.entity';
import { GetSignalsQueryDto } from './dto/get-signals-query.dto';

@Injectable()
export class SignalService {
  constructor(
    private readonly em: EntityManager,
  ) { }

  async find(getSignalsQueryDto: GetSignalsQueryDto) {
    const query = this.em.createQueryBuilder(Signal, 'signal');

    if (getSignalsQueryDto.is_active !== undefined && getSignalsQueryDto.is_active !== null) {
      query.andWhere('signal.is_active = :is_active', { is_active: getSignalsQueryDto.is_active });
    }

    if (getSignalsQueryDto.market) {
      query.andWhere('signal.market = :market', { market: getSignalsQueryDto.market });
    }

    const signals = await query.getMany()

    return signals
  }
}
