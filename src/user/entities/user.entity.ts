import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { Exclude } from 'class-transformer';
import { Deposit } from 'src/deposit/entities/deposit.entity';
import { UserPair } from 'src/user_pair/entities/user_pair.entity';
import { Order } from 'src/order/entities/order.entity';
import { Summary } from 'src/summary/entities/summary.entity';

@Entity()
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column({ unique: true })
  email: string;

  @Column({ nullable: true })
  @Exclude()
  email_verified_at: Date;

  @Column()
  @Exclude()
  password: string;

  @Column({ default: null, nullable: true })
  @Exclude()
  temp_password: string;

  @Column({ nullable: true })
  @Exclude()
  g2a: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @OneToMany(() => Deposit, (deposit) => deposit.user)
  deposits: Deposit[]

  @OneToMany(() => UserPair, (userPair) => userPair.user)
  userPairs: UserPair[]

  @OneToMany(() => Order, (order) => order.user)
  orders: Order[]

  @OneToMany(() => Summary, (summary) => summary.user)
  summmaries: Summary[]
}
