import { Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import {
  HttpException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import configuration from 'config/configuration';
import { EntityManager, Equal } from 'typeorm';
import * as jwt from 'jsonwebtoken';
import { user2FACookieExtractor } from './cookie-extractor';
import { User } from 'src/user/entities/user.entity';

@Injectable()
export class Jwt2FAStrategy extends PassportStrategy(
  Strategy,
  'your-own-cookies-2fa',
) {
  constructor(private readonly em: EntityManager) {
    super({
      jwtFromRequest: user2FACookieExtractor(),
      ignoreExpiration: false,
      secretOrKey: configuration().jwtConstant.jwtAccessSecret,
      passReqToCallback: true,
    });
  }

  async validate(payload: any) {
    if (!payload.headers.cookie) {
      throw new UnauthorizedException('please provide valid token');
    }
    if (payload.headers.cookie.search('your-own-cookies-2fa=') == -1) {
      throw new UnauthorizedException('invalid token');
    }

    try {
      let token_1 = payload.headers.cookie.split(' ');
      let token = token_1
        .find((item) => item.search('your-own-cookies-2fa=') != -1)
        .split('your-own-cookies-2fa=')[1];
      if (token[token.length - 1] == ';') {
        token = token.slice(0, -1);
      }
      //verify token
      var decode = jwt.verify(token, configuration().jwtConstant.jwtAccessSecret);
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new UnauthorizedException('token is expired');
      }
      if (error.name === 'user is not valid') {
        throw new UnauthorizedException('user is not valid');
      }
      if (error.name === 'please provide valid token') {
        throw new UnauthorizedException('please provide valid token');
      }
      throw new HttpException(error.message, error.status);
    }
    let user = await this.em.findOneBy(User, {
      email: Equal(decode['email']),
    });
    if (!user) {
      throw new UnauthorizedException('not an user account');
    }
    // else if (decode['is_2fa'] !== true) {
    //   throw new UnauthorizedException(
    //     'Please bind 2fa before proceed 2fa login',
    //   );
    // }
    return decode;
  }
}
