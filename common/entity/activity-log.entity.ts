import { Admin } from "src/admin/entities/admin.entity";
import {
	Column,
	CreateDateColumn,
	Entity,
	JoinColumn,
	ManyToOne,
	PrimaryGeneratedColumn
} from "typeorm";

export enum LogEvent {
	LOGIN = 'login',
	LOGIN_2FA = 'login-2fa',
	REGISTER = 'register',
	RESET_PASSWORD = 'reset-password',
	CHANGE_PASSWORD = 'change-password',
	REFRESH = 'refresh-access-token',
	LOGOUT = 'logout',
	GET_API_KEYS = 'get-api-keys',
	TOGGLE_API_STATUS = 'toggle-api-status',
	RECREATE_LISTEN_KEYS = 'recreate-listen-keys',
	CREATE_PLATFORM = 'create-platform',
	UPDATE_PLATFORM = 'update-platform',
	CREATE_SIGNAL = 'create-signal',
	UPDATE_SIGNAL = 'update-signal',
	CREATE_PAIR = 'create-pair',
	UPDATE_PAIR = 'update-pair',
}

export enum EventStatus {
	SUCCESS = 'SUCCESS',
	FAILED = 'FAILED',
}

@Entity()
export class ActivityLog {
	@PrimaryGeneratedColumn()
	id: number;

	@Column({ type: "enum", enum: LogEvent })
	event: LogEvent;

	@Column('longtext', { nullable: true })
	old_values: string;

	@Column('longtext', { nullable: true })
	new_values: string;

	@Column({ nullable: true })
	endpoint: string;

	@Column({ nullable: true })
	ip_address: string;

	@Column({ nullable: true })
	user_agent: string;

	@Column({ type: "enum", enum: EventStatus })
	status: EventStatus;

	@Column('longtext', { nullable: true })
	error_message: string;

	@ManyToOne((type) => Admin, (admin) => admin.activity_log)
	@JoinColumn({ name: 'admin_id' })
	admin: Admin;

	@CreateDateColumn()
	created_at: string;
}