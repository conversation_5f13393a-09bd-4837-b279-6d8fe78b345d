import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsNumber, IsOptional, IsString, Min } from 'class-validator';
import { FeeSource } from '../entities/order_fee.entity';

export class GetOrderFeeQueryDto {
    @ApiPropertyOptional({ description: 'Total fees for specific order Id' })
    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    @Min(1)
    order_id: number

    @ApiPropertyOptional({ description: 'Asset Name' })
    @IsOptional()
    @IsString()
    asset: string

    @ApiPropertyOptional({ description: 'Source Type' })
    @IsOptional()
    @IsEnum(FeeSource)
    source: FeeSource
}