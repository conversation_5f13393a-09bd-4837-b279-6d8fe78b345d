import { Controller, Get, Query } from '@nestjs/common';
import { PairService } from './pair.service';
import { GetPairQueryDto } from './dto/get-pair-query.dto';
import { ApiOperation, ApiQuery } from '@nestjs/swagger';
import { PairStatus } from 'common/interface/pair-status.enum';
import { MarketType } from 'common/interface/market-type.enum';

@Controller('pair')
export class PairController {
  constructor(
    private readonly pairService: PairService
  ) { }

  @ApiOperation({
    summary: 'Get all trading pairs',
    description: 'Get all trading pairs with optional filtering by platform ID, pair name, market type, and pair status'
  })
  @ApiQuery({
    name: 'platform_id',
    required: false,
    type: Number,
    description: 'Platform ID',
  })
  @ApiQuery({
    name: 'name',
    required: false,
    type: String,
    description: 'Pair name',
  })
  @ApiQuery({
    name: 'market',
    required: false,
    enum: MarketType,
    description: 'Market type',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: PairStatus,
    description: 'Pair status',
  })
  @Get()
  async find(@Query() getPairQueryDto: GetPairQueryDto) {
    return await this.pairService.find(getPairQueryDto);
  }
}
