import { DecimalJsTransformer } from 'common/util/decimal-js-transformer';
import Decimal from 'decimal.js';
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity()
export class Profit {
  @PrimaryGeneratedColumn({ type: 'bigint', unsigned: true })
  id: number;

  // softlink
  @Column()
  buy_id: string;

  // softlink
  @Column()
  sell_id: string;

  @Column({
    default: '0',
    type: 'decimal',
    precision: 25,
    scale: 8,
    transformer: DecimalJsTransformer,
  })
  qty: string;

  @Column({
    default: '0',
    type: 'decimal',
    precision: 25,
    scale: 8,
    transformer: DecimalJsTransformer,
  })
  profit: string;

  @Column({
    default: '0',
    type: 'decimal',
    precision: 25,
    scale: 8,
    transformer: DecimalJsTransformer,
  })
  profit_pct: string;

  @Column({ type: 'boolean' })
  is_long: boolean;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
