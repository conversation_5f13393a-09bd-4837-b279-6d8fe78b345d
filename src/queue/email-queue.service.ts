import { OnQueueActive, OnQueueCompleted, OnQueueFailed, Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { MailerService } from '@nestjs-modules/mailer';
import { PinoLogger } from 'nestjs-pino';
import configuration from 'config/configuration';

type EmailData = {
  email: string
  temp_password: string
  name: string
}

@Processor('email')
export class EmailQueueService {
  constructor(
    private readonly mailerService: MailerService,
    private readonly pinoLogger: PinoLogger
  ) {
    this.pinoLogger.setContext(EmailQueueService.name)
    this.pinoLogger.info('EmailQueueService is initialized and ready.')
  }

  // @Process('otp')
  // async readOperationJob4(job: Job) {
  //   console.log('otp lor');
  //   let otp = job.data.otp;
  //   let email = job.data.email;

  //   return this.mailerService
  //     .sendMail({
  //       to: email, // List of receivers email address
  //       from: '<EMAIL>', // Senders email address
  //       subject: 'Request for OTP', // Subject line
  //       template: 'otp', // HTML body content
  //       context: {
  //         otp,
  //         email,
  //       },
  //     })
  //     .then((success) => {
  //       console.log(success);
  //     })
  //     .catch((err) => {
  //       console.log(err);
  //     });
  // }

  @Process('reset')
  async readOperationJob1(job: Job<EmailData>) {
    const { email, temp_password, name } = job.data

    return this.mailerService
      .sendMail({
        to: email, // List of receivers email address
        from: configuration().email.user, // Senders email address
        subject: 'EZBOT: Request for reset password', // Subject line
        template: 'reset-password', // HTML body content
        context: {
          temp_password,
          name,
        },
      })
      .then((success) => {
        this.pinoLogger.info(success);
      })
      .catch((err) => {
        this.pinoLogger.error(err);
      });
  }

  @OnQueueActive()
  onActive(job: Job<EmailData>) {
    this.pinoLogger.info(
      `[ACTIVE] Job #${job.id} - Sending reset password email to ${job.data.email}`
    )
  }

  @OnQueueCompleted()
  onCompleted(job: Job<EmailData>, result: any) {
    const { email, name } = job.data
    this.pinoLogger.info(
      `[COMPLETED] Job #${job.id} - Temporary password have been send to ${name}: ${email}`
    )
  }
  
  @OnQueueFailed()
  async onFailed(job: Job<EmailData>, err: Error) {
    const { email, name } = job.data
    this.pinoLogger.error(
      `[FAILED] Job #${job.id} - Failed to send email to ${name}: ${email}. Reason: ${err.message}`
    )
  }
}
