import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsInt, IsNotEmpty, IsNumberString, IsOptional, IsString, Min } from "class-validator";

export class ManageFundsDto {
    @ApiPropertyOptional({ description: "Sub account's user ID", example: 2 })
    @IsOptional()
    @IsInt()
    @Min(1)
    sub_user_id?: number;

    @ApiProperty({ description: 'Signal ID', example: 17 })
    @IsNotEmpty()
    @IsInt()
    signal_id: number

    @ApiProperty({ description: "Coin", example: 'FDUSD' })
    @IsNotEmpty()
    @IsString()
    coin: string;

    @ApiProperty({ description: "Quantity", example: '-5000' })
    @IsNotEmpty()
    @IsNumberString()
    qty: string;

    @ApiPropertyOptional({ description: "Reason for allocation/deallocation", example: 'Bad performance' })
    @IsOptional()
    @IsString()
    reason?: string;
}