import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsString } from 'class-validator';

export class RegisterUserDto {
  @ApiProperty({ description: 'User email', example: '<EMAIL>' })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({ description: 'User password', example: 'abc123' })
  @IsNotEmpty()
  @IsString()
  password: string;

  @ApiProperty({ description: 'User confirm password', example: 'abc123' })
  @IsNotEmpty()
  @IsString()
  confPassword: string;

  @ApiProperty({ description: 'User name', example: 'KK' })
  @IsNotEmpty()
  @IsString()
  name: string;
}
