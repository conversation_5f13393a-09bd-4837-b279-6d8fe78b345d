import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateNewsDto {
    @ApiProperty({ description: 'news title' })
    @IsNotEmpty()
    @IsString()
    title: string

    @ApiProperty({ description: 'news source' })
    @IsNotEmpty()
    @IsString()
    source: string

    @ApiProperty({ description: 'news url' })
    @IsNotEmpty()
    @IsString()
    url: string

    @ApiPropertyOptional({ description: 'keywords related to news' })
    @IsOptional()
    @IsString()
    related?: string

    @ApiPropertyOptional({ description: "news' preview image url" })
    @IsOptional()
    @IsString()
    preview_img?: string

    @ApiProperty({ description: 'news published datetime', type: String, format: 'date-time' })
    @IsNotEmpty()
    published_at: Date
}
