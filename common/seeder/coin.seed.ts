import { <PERSON><PERSON><PERSON>og<PERSON> } from "nestjs-pino";
import { Coin } from "src/coin/entities/coin.entity";
import { DataSource } from "typeorm";

export async function seederCoin(dataSource: DataSource) {
    const pinoLogger = new PinoLogger({ pinoHttp: {} })
    const repo = dataSource.getRepository(Coin)
    const count = await repo.count()

    pinoLogger.setContext(seederCoin.name)

    if (count === 0) {
        const coins: Partial<Coin>[] = [
            { name: 'Bitcoin', symbol: 'BTC', vol_dp: 8, price_dp: 2 },
            { name: 'Ethereum', symbol: 'ETH', vol_dp: 4, price_dp: 2 },
            { name: 'Binance Coin', symbol: 'BNB', vol_dp: 3, price_dp: 1 },
            { name: 'Litecoin', symbol: 'LTC', vol_dp: 3, price_dp: 2 },
            { name: '<PERSON><PERSON><PERSON>', symbol: 'X<PERSON>', vol_dp: 0, price_dp: 4 },
            { name: 'Polygon', symbol: '<PERSON><PERSON>', vol_dp: 1, price_dp: 4 },
            { name: '<PERSON><PERSON><PERSON><PERSON>', symbol: 'DOGE', vol_dp: 0, price_dp: 5 },
            { name: 'Solana', symbol: 'SOL', vol_dp: 3, price_dp: 2 },
            { name: 'Cardano', symbol: 'ADA', vol_dp: 1, price_dp: 4 },
            { name: 'Filecoin', symbol: 'FIL', vol_dp: 2, price_dp: 3 },
            { name: 'Render Token', symbol: 'RENDER', vol_dp: 2, price_dp: 3 },
            { name: 'Avalanche', symbol: 'AVAX', vol_dp: 2, price_dp: 2 },
            { name: 'Opacity', symbol: 'OP', vol_dp: 2, price_dp: 3 },
            { name: 'Chainlink', symbol: 'LINK', vol_dp: 2, price_dp: 3 },
            { name: 'Arbitrum', symbol: 'ARB', vol_dp: 1, price_dp: 4 },
            { name: 'Shiba Inu', symbol: 'SHIB', vol_dp: 0, price_dp: 8 },
            { name: 'The Open Network', symbol: 'TON', vol_dp: 2, price_dp: 3 },
            { name: 'SUI', symbol: 'SUI', vol_dp: 1, price_dp: 4 },
            { name: 'Polkadot', symbol: 'DOT', vol_dp: 2, price_dp: 3 },
            { name: 'Tron', symbol: 'TRX', vol_dp: 0, price_dp: 4 },
            { name: 'First Digital USD', symbol: 'FDUSD', vol_dp: 0, price_dp: 4 },
        ]

        await repo.save(coins)
        pinoLogger.info('[SEED] Coin table seeded.')
    }
}