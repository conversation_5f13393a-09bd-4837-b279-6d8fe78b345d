
import { ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsEnum, IsInt, IsOptional, IsString, Min } from "class-validator";
import { MarketType } from "common/interface/market-type.enum";
import { PairStatus } from "common/interface/pair-status.enum";

export class GetPairQueryDto {
  @ApiPropertyOptional({ description: 'Platform ID', example: '1' })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  platform_id?: number;

  @ApiPropertyOptional({ description: 'Pair name (partial match allowed)', example: 'BTC' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: 'Market type', enum: MarketType, example: MarketType.SPOT })
  @IsOptional()
  @IsEnum(MarketType)
  market?: MarketType

  @ApiPropertyOptional({ description: 'Pair status', enum: PairStatus, example: PairStatus.ACTIVE })
  @IsOptional()
  @IsEnum(PairStatus)
  status?: PairStatus
}