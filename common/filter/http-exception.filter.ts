import {
  Catch,
  HttpException,
  ExceptionFilter,
  ArgumentsHost,
} from '@nestjs/common';
import { Response, Request } from 'express';
import { QueryFailedError } from 'typeorm';

//catch the exception name HttpException
@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();

    if (exception instanceof HttpException) {
      let status = exception.getStatus();

      //this is the return format of the httpException
      return response.status(status).json({
        status: 'fail',
        message: exception.message,
      });
    } else if (exception instanceof QueryFailedError) {
      return response.status(400).json({
        status: 'fail',
        message: exception.message,
      });
    }
    //this is the return format of the httpException
  }
}

@Catch()
export class ValidationExceptionFilter implements ExceptionFilter {
  public catch(exception, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const status = exception.getStatus();
    return (
      response
        .status(status)
        // you can manipulate the response here
        .json({
          statusCode: status,
          timestamp: new Date().toISOString(),
          path: request.url,
        })
    );
  }
}
