import { ApiProperty } from "@nestjs/swagger";
import { IsBoolean, IsInt, IsNotEmpty, IsNumberString, Min } from "class-validator";

export class UpdateUserPairDto {
    @ApiProperty({ description: 'User pair ID', example: 1 })
    @IsNotEmpty()
    @IsInt()
    id: number;

    @ApiProperty({ description: 'Pair ID', example: 1 })
    @IsNotEmpty()
    @IsInt()
    @Min(1)
    pair_id?: number;

    @ApiProperty({ description: 'Signal ID', example: 1 })
    @IsNotEmpty()
    @IsInt()
    @Min(1)
    signal_id?: number;

    @ApiProperty({ description: 'Order quantity', example: '0.1' })
    @IsNotEmpty()
    @IsNumberString()
    qty?: string;

    @ApiProperty({ description: 'Step size', example: '5.0' })
    @IsNotEmpty()
    @IsNumberString()
    step?: string;

    @ApiProperty({ description: 'Whether the pair is active', example: true })
    @IsNotEmpty()
    @IsBoolean()
    is_active?: boolean;

    @ApiProperty({ description: 'Whether to enable automatic entry', example: false })
    @IsNotEmpty()
    @IsBoolean()
    auto_entry?: boolean;

    @ApiProperty({ description: 'Gap percentage between orders', example: '2.0' })
    @IsNotEmpty()
    @IsNumberString()
    gap?: string;

    @ApiProperty({ description: 'Maximum number of orders', example: 3 })
    @IsNotEmpty()
    @IsInt()
    max_orders?: number;

    @ApiProperty({ description: 'Whether to enable automatic take profit', example: false })
    @IsNotEmpty()
    @IsBoolean()
    auto_tp?: boolean;

    @ApiProperty({ description: 'Take profit percentage', example: '2.0' })
    @IsNotEmpty()
    @IsNumberString()
    tp?: string;

    @ApiProperty({ description: 'Whether to enable automatic stop loss', example: false })
    @IsNotEmpty()
    @IsBoolean()
    auto_sl?: boolean;

    @ApiProperty({ description: 'Stop loss percentage', example: '1.8' })
    @IsNotEmpty()
    @IsNumberString()
    sl?: string;
}
