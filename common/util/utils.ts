import { HttpException } from '@nestjs/common';
import configuration from 'config/configuration';
import * as crypto from 'crypto';
import * as base32 from 'hi-base32';
import Decimal from 'decimal.js';
import axios from 'axios';

export async function encrypted(data: any, random: any): Promise<any> {
  return new Promise(async (resolve, reject) => {
    const algorithm = 'aes-128-cbc';
    const password = configuration().jwtConstant.jwtAccessSecret;
    crypto.scrypt(password, 'salt', 16, (err, key) => {
      if (err) throw err;
      // Then, we'll generate a random initialization vector
      const iv = Buffer.alloc(16, random); // Initialization vector.
      if (err) throw err;
      const cipher = crypto.createCipheriv(algorithm, key, iv);

      let encrypted = cipher.update(data, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      resolve(encrypted);
    });
  });
}

export async function decrypted(data: any, random: any): Promise<any> {
  return new Promise(async (resolve, reject) => {
    const algorithm = 'aes-128-cbc';
    const password = configuration().jwtConstant.jwtAccessSecret;
    const key = crypto.scrypt(password, 'salt', 16, (err, key) => {
      // The IV is usually passed along with the ciphertext.
      const iv = Buffer.alloc(16, random); // Initialization vector.

      const decipher = crypto.createDecipheriv(algorithm, key, iv);

      // Encrypted using same algorithm, key and iv.
      const encrypted = data;
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      resolve(decrypted);
    });
  });
}

export async function generateSecret(length = 20) {
  const randomBuffer = crypto.randomBytes(length);
  let r = base32.encode(randomBuffer).replace(/=/g, '');
  return r;
}
function generateHOTP(secret, counter) {
  const decodedSecret = base32.decode.asBytes(secret);
  const buffer = Buffer.alloc(8);
  for (let i = 0; i < 8; i++) {
    buffer[7 - i] = counter & 0xff;
    counter = counter >> 8;
  }

  // Step 1: Generate an HMAC-SHA-1 value
  const hmac = crypto.createHmac('sha1', Buffer.from(decodedSecret));
  hmac.update(buffer);
  const hmacResult = hmac.digest();

  // Step 2: Generate a 4-byte string (Dynamic Truncation)
  const code = dynamicTruncationFn(hmacResult);

  // Step 3: Compute an HOTP value
  return code % 10 ** 6;
}

function dynamicTruncationFn(hmacValue) {
  const offset = hmacValue[hmacValue.length - 1] & 0xf;

  return (
    ((hmacValue[offset] & 0x7f) << 24) |
    ((hmacValue[offset + 1] & 0xff) << 16) |
    ((hmacValue[offset + 2] & 0xff) << 8) |
    (hmacValue[offset + 3] & 0xff)
  );
}
export function generateTOTP(secret, window = 0) {
  const counter = Math.floor(Date.now() / 30000);
  return generateHOTP(secret, counter + window)
    .toString()
    .padStart(6, '0');
}

export function verify2FA(secret, OTP: string) {
  let systemOTP = generateTOTP(secret).toString().padStart(6, '0');
  if (systemOTP == OTP) {
    return true;
  }
  throw new HttpException('Invalid 2FA', 400);
}

/**
 * Helper function to sort and stringify query parameters
 * @param {Record<string, any>} params - A record of parameters
 * @returns {string} - A URL string
 */
export function sortParams(params: Record<string, any>): string {
  return Object.keys(params)
    .sort()
    .map((key) => `${key}=${encodeURIComponent(params[key])}`)
    .join('&');
}

/**
 * Helper function to sort and stringify JSON object
 * @param {Record<string, any>} json - JSON object
 * @returns {string} - A JSON string
 */
export function sortJson(json: Record<string, any>): string {
  const sortedJson = Object.keys(json)
    .sort()
    .reduce(
      (acc, key) => {
        acc[key] = json[key];
        return acc;
      },
      {} as Record<string, any>,
    );
  return JSON.stringify(sortedJson);
}

/**
 * Helper function to remove null or undefined values from an object
 * @param {Record<string, any>} obj - JSON object
 * @returns {string} - A JSON string
 */
export function cleanObject(obj: Record<string, any>): Record<string, any> {
  return Object.keys(obj).reduce(
    (acc, key) => {
      if (obj[key] !== null && obj[key] !== undefined) {
        acc[key] = obj[key];
      }
      return acc;
    },
    {} as Record<string, any>,
  );
}

/**
 * Helper function to count decimal of a given number
 * @param {number} num - Given number
 * @returns {number} - Number of decimals
 */
export function countDecimals(num: string): number {
  return (num.split('.')[1] || []).length;
}

export function truncateToDecimals(amount, decimal) {
  return new Decimal(amount.mul(10 ** decimal).floor()).div(10 ** decimal);
}

export function roundToDecimals(amount, decimal) {
  const factor = new Decimal(10).pow(decimal);
  return amount.mul(factor).ceil().div(factor);
}

export async function refreshConnection() {
  try {
    const response = await axios.put(configuration().stream_url, null);
    // console.log('Response:', response.data);
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

export async function addSocketConnection(pair: string, platform: string) {
  try {
    const body: Record<string, any> = {
      pair: pair,
      platform,
    };
    const response = await axios.post(
      configuration().stream_url + 'exchange/price',
      body,
      null,
    );
    // console.log('Response:', response.data);
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

export async function removeSocketConnection(pairId: string): Promise<void> {
  const base = configuration().stream_url.replace(/\/$/, '')    // strip trailing slash
  const url = `${base}/exchange/price/unsubscribe`;

  try {
    const res = await axios.post(url,
      { pair_id: pairId },
      { timeout: 10000 }
    );

  } catch (err) {
    if (err.response) {
      console.error(`Status: ${err.response.status}, Data:`, err.response.data);
    } else if (err.request) {
      console.error(`No response received. Request was sent to: ${url}`);
    } else {
      console.error(`Error message: ${err.message}`);
    }
    throw err;
  }
}