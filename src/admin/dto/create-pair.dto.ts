import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsInt, IsNotEmpty, IsNumberString, IsString, Min } from 'class-validator';
import { MarketType } from 'common/interface/market-type.enum';
import { PairStatus } from 'common/interface/pair-status.enum';

export class CreatePairDto {
  @ApiProperty({ description: 'Platform ID', example: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  platform_id: number;

  @ApiProperty({ description: 'Market type', example: MarketType.SPOT })
  @IsNotEmpty()
  @IsEnum(MarketType)
  market: MarketType;

  @ApiProperty({ description: 'Base currency', example: 'BTC' })
  @IsNotEmpty()
  @IsString()
  base: string;

  @ApiProperty({ description: 'Quote currency', example: 'FDUSD' })
  @IsNotEmpty()
  @IsString()
  quote: string;

  @ApiProperty({ description: 'Price decimal precision', example: 5 })
  @IsNotEmpty()
  @IsInt()
  @Min(0)
  price_dp: number;

  @ApiProperty({ description: 'Volume decimal precision', example: 2 })
  @IsNotEmpty()
  @IsInt()
  @Min(0)
  volume_dp: number;

  @ApiProperty({ description: 'Pair status', default: PairStatus.ACTIVE })
  @IsNotEmpty()
  @IsEnum(PairStatus)
  status: PairStatus;

  @ApiProperty({ description: 'Pair default step size', default: '5.0' })
  @IsNotEmpty()
  @IsNumberString()
  default_step: string
}