import { Injectable } from '@nestjs/common';
import { CreateNewsDto } from './dto/create-news.dto';
import { EntityManager } from 'typeorm';
import { News } from './entities/news.entity';
import { FilterNewsDto } from './dto/filter-news.dto';

@Injectable()
export class NewsService {
  constructor(
    private readonly em: EntityManager
  ) { }

  async create(createNewsDto: CreateNewsDto) {
    let news = new News();

    news.title = createNewsDto.title
    news.source = createNewsDto.source
    news.url = createNewsDto.url
    news.related = createNewsDto.related
    news.preview_img = createNewsDto.preview_img
    news.published_at = createNewsDto.published_at

    await this.em.save(news)

    return 'News has been stored'
  }

  async filterNews(filterNewsDto: FilterNewsDto) {
    const query = this.em.createQueryBuilder(News, 'n').select()

    if (filterNewsDto.keyword) {
      query.andWhere(
        '(n.title LIKE :keyword OR n.related LIKE :keyword)',
        { keyword: `%${filterNewsDto.keyword}%` }
      )
    }

    if (filterNewsDto.source) {
      query.andWhere('n.source = :source', { source: filterNewsDto.source })
    }

    const news = await query.orderBy('n.published_at', 'DESC').getMany()

    return news
  }
}
