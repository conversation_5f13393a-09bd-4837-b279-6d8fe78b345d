import { Modu<PERSON> } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { JwtStrategy } from './jwt.strategy';
import { JwtAdminStrategy } from './jwt-admin.strategy';
import configuration from 'config/configuration';
import { Jwt2FAStrategy } from './jwt-2fa.strategy';

@Module({
  imports: [
    JwtModule.register({
      secret: configuration().jwtConstant.jwtAccessSecret,
      signOptions: { expiresIn: configuration().jwtConstant.jwtAccessExpired },
    }),
  ],

  providers: [JwtStrategy, JwtAdminStrategy, Jwt2FAStrategy],
  controllers: [],
  exports: [AuthModule],
})
export class AuthModule { }
