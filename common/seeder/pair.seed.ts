import { DataSource, Equal } from "typeorm";
import { MarketType } from "common/interface/market-type.enum";
import { Platform } from "src/platform/entities/platform.entity";
import { PairStatus } from "common/interface/pair-status.enum";
import { Pair } from "src/pair/entities/pair.entity";
import { <PERSON>noLogger } from "nestjs-pino";

export async function seederPair(dataSource: DataSource) {
    const pinoLogger = new PinoLogger({ pinoHttp: {} })
    const repo = dataSource.getRepository(Pair)
    const count = await repo.count()

    pinoLogger.setContext(seederPair.name)

    if (count === 0) {
        const binance = await dataSource.getRepository(Platform).findOneBy({ name: Equal("Binance") });
        if (!binance) {
            pinoLogger.error("Platform Binance not found, skipping seeding Pair process");
            return;
        }

        await repo.save([
            {
                platform: binance,
                name: 'BNBFDUSD',
                market: MarketType.SPOT,
                base: 'BNB',
                quote: 'FDUSD',
                price_dp: 2,
                volume_dp: 3,
                default_step: '0.05',
            },
            {
                platform: binance,
                name: 'POLFDUSD',
                market: MarketType.SPOT,
                base: 'POL',
                quote: 'FDUSD',
                price_dp: 4,
                volume_dp: 1,
                default_step: '0.0005',
            },
            {
                platform: binance,
                name: 'SOLFDUSD',
                market: MarketType.SPOT,
                base: 'SOL',
                quote: 'FDUSD',
                price_dp: 2,
                volume_dp: 3,
                default_step: '0.01',
            },
            {
                platform: binance,
                name: 'ADAFDUSD',
                market: MarketType.SPOT,
                base: 'ADA',
                quote: 'FDUSD',
                price_dp: 4,
                volume_dp: 1,
                default_step: '0.0002',
            },
            {
                platform: binance,
                name: 'XRPFDUSD',
                market: MarketType.SPOT,
                base: 'XRP',
                quote: 'FDUSD',
                price_dp: 4,
                volume_dp: 0,
                default_step: '0.00001',
            },
            {
                platform: binance,
                name: 'LTCFDUSD',
                market: MarketType.SPOT,
                base: 'LTC',
                quote: 'FDUSD',
                price_dp: 2,
                volume_dp: 3,
                default_step: '0.01',
            },
            {
                platform: binance,
                name: 'MATICFDUSD',
                market: MarketType.SPOT,
                base: 'MATIC',
                quote: 'FDUSD',
                price_dp: 4,
                volume_dp: 1,
                default_step: '0.0002',
            },
            {
                platform: binance,
                name: 'SHIBFDUSD',
                market: MarketType.SPOT,
                base: 'SHIB',
                quote: 'FDUSD',
                price_dp: 8,
                volume_dp: 0,
                default_step: '0.00000001',
            },
            {
                platform: binance,
                name: 'ARBFDUSD',
                market: MarketType.SPOT,
                base: 'ARB',
                quote: 'FDUSD',
                price_dp: 4,
                volume_dp: 1,
                default_step: '0.0001',
            },
            {
                platform: binance,
                name: 'LINKFDUSD',
                market: MarketType.SPOT,
                base: 'LINK',
                quote: 'FDUSD',
                price_dp: 2,
                volume_dp: 2,
                default_step: '0.01',
            },
            {
                platform: binance,
                name: 'OPFDUSD',
                market: MarketType.SPOT,
                base: 'OP',
                quote: 'FDUSD',
                price_dp: 3,
                volume_dp: 2,
                default_step: '0.002',
            },
            {
                platform: binance,
                name: 'ETHFDUSD',
                market: MarketType.SPOT,
                base: 'ETH',
                quote: 'FDUSD',
                price_dp: 2,
                volume_dp: 4,
                default_step: '0.5',
            },
            {
                platform: binance,
                name: 'AVAXFDUSD',
                market: MarketType.SPOT,
                base: 'AVAX',
                quote: 'FDUSD',
                price_dp: 2,
                volume_dp: 2,
                default_step: '0.01',
            },
            {
                platform: binance,
                name: 'RENDERFDUSD',
                market: MarketType.SPOT,
                base: 'RENDER',
                quote: 'FDUSD',
                price_dp: 3,
                volume_dp: 2,
                default_step: '0.001',
            },
            {
                platform: binance,
                name: 'FILFDUSD',
                market: MarketType.SPOT,
                base: 'FIL',
                quote: 'FDUSD',
                price_dp: 3,
                volume_dp: 2,
                default_step: '0.001',
            },
            {
                platform: binance,
                name: 'BTCFDUSD',
                market: MarketType.SPOT,
                base: 'BTC',
                quote: 'FDUSD',
                price_dp: 2,
                volume_dp: 5,
                default_step: '5',
            },
            {
                platform: binance,
                name: 'DOGEFDUSD',
                market: MarketType.SPOT,
                base: 'DOGE',
                quote: 'FDUSD',
                price_dp: 5,
                volume_dp: 0,
                default_step: '0.00002',
            },
            {
                platform: binance,
                name: 'TONFDUSD',
                market: MarketType.SPOT,
                base: 'TON',
                quote: 'FDUSD',
                price_dp: 3,
                volume_dp: 2,
                default_step: '0.001',
            },
            {
                platform: binance,
                name: 'RNDRFDUSD',
                market: MarketType.SPOT,
                base: 'RNDR',
                quote: 'FDUSD',
                price_dp: 3,
                volume_dp: 2,
                status: PairStatus.INACTIVE,
                default_step: '0.002',
            },
            {
                platform: binance,
                name: 'DOTFDUSD',
                market: MarketType.SPOT,
                base: 'DOT',
                quote: 'FDUSD',
                price_dp: 3,
                volume_dp: 2,
                default_step: '0.001',
            },
            {
                platform: binance,
                name: 'SUIFDUSD',
                market: MarketType.SPOT,
                base: 'SUI',
                quote: 'FDUSD',
                price_dp: 4,
                volume_dp: 1,
                default_step: '0.0001',
            },
            {
                platform: binance,
                name: 'TRXFDUSD',
                market: MarketType.SPOT,
                base: 'TRX',
                quote: 'FDUSD',
                price_dp: 5,
                volume_dp: 1,
                default_step: '0.0001',
            },
            {
                platform: binance,
                name: 'SOLUSDT',
                market: MarketType.SPOT,
                base: 'SOL',
                quote: 'USDT',
                price_dp: 3,
                volume_dp: 5,
                status: PairStatus.INACTIVE,
                default_step: '0.01',
            },
            {
                platform: binance,
                name: 'LINKUSDT',
                market: MarketType.SPOT,
                base: 'LINK',
                quote: 'USDT',
                price_dp: 2,
                volume_dp: 2,
                status: PairStatus.INACTIVE,
                default_step: '0.01',
            },
            {
                platform: binance,
                name: 'FILUSDT',
                market: MarketType.SPOT,
                base: 'FIL',
                quote: 'USDT',
                price_dp: 3,
                volume_dp: 2,
                status: PairStatus.INACTIVE,
                default_step: '0.001',
            },
            {
                platform: binance,
                name: 'BTCUSDT',
                market: MarketType.SPOT,
                base: 'BTC',
                quote: 'USDT',
                price_dp: 2,
                volume_dp: 5,
                status: PairStatus.INACTIVE,
                default_step: '5',
            },
            {
                platform: binance,
                name: 'ETHUSDT',
                market: MarketType.SPOT,
                base: 'ETH',
                quote: 'USDT',
                price_dp: 2,
                volume_dp: 4,
                status: PairStatus.INACTIVE,
                default_step: '0.5',
            },
            {
                platform: binance,
                name: 'MATICUSDT',
                market: MarketType.SPOT,
                base: 'MATIC',
                quote: 'USDT',
                price_dp: 4,
                volume_dp: 1,
                status: PairStatus.INACTIVE,
                default_step: '0.0002',
            },
            {
                platform: binance,
                name: 'BNBUSDT',
                market: MarketType.SPOT,
                base: 'BNB',
                quote: 'USDT',
                price_dp: 1,
                volume_dp: 3,
                status: PairStatus.INACTIVE,
                default_step: '0.5',
            },
            {
                platform: binance,
                name: 'LTCUSDT',
                market: MarketType.SPOT,
                base: 'LTC',
                quote: 'USDT',
                price_dp: 3,
                volume_dp: 4,
                status: PairStatus.INACTIVE,
                default_step: '0.01',
            },
            {
                platform: binance,
                name: 'SHIBUSDT',
                market: MarketType.SPOT,
                base: 'SHIB',
                quote: 'USDT',
                price_dp: 8,
                volume_dp: 0,
                status: PairStatus.INACTIVE,
                default_step: '0.00000001',
            },
            {
                platform: binance,
                name: 'XRPUSDT',
                market: MarketType.SPOT,
                base: 'XRP',
                quote: 'USDT',
                price_dp: 4,
                volume_dp: 0,
                status: PairStatus.INACTIVE,
                default_step: '0.00001',
            },
            {
                platform: binance,
                name: 'AVAXUSDT',
                market: MarketType.SPOT,
                base: 'AVAX',
                quote: 'USDT',
                price_dp: 3,
                volume_dp: 4,
                status: PairStatus.INACTIVE,
                default_step: '0.01',
            },
            {
                platform: binance,
                name: 'FDUSDUSDT',
                market: MarketType.SPOT,
                base: 'FDUSD',
                quote: 'USDT',
                price_dp: 5,
                volume_dp: 5,
                default_step: '0.00002',
            },
            {
                platform: binance,
                name: 'USDCUSDT',
                market: MarketType.SPOT,
                base: 'USDC',
                quote: 'USDT',
                price_dp: 5,
                volume_dp: 5,
                default_step: '0.00002',
            },
            {
                platform: binance,
                name: 'ADAUSDT',
                market: MarketType.SPOT,
                base: 'ADA',
                quote: 'USDT',
                price_dp: 4,
                volume_dp: 1,
                status: PairStatus.INACTIVE,
                default_step: '0.0002',
            },
            {
                platform: binance,
                name: 'BNBUSDC',
                market: MarketType.FUTURE,
                base: 'BNB',
                quote: 'USDC',
                price_dp: 2,
                volume_dp: 2,
                default_step: '0.05',
            },
            {
                platform: binance,
                name: 'BTCUSDC',
                market: MarketType.FUTURE,
                base: 'BTC',
                quote: 'USDC',
                price_dp: 1,
                volume_dp: 3,
                default_step: '5',
            },
            {
                platform: binance,
                name: 'LTCUSDT',
                market: MarketType.FUTURE,
                base: 'LTC',
                quote: 'USDT',
                price_dp: 2,
                volume_dp: 4,
                status: PairStatus.INACTIVE,
                default_step: '0.01',
            },
            {
                platform: binance,
                name: 'BNBUSDT',
                market: MarketType.FUTURE,
                base: 'BNB',
                quote: 'USDT',
                price_dp: 1,
                volume_dp: 3,
                status: PairStatus.INACTIVE,
                default_step: '0.05',
            },
            {
                platform: binance,
                name: 'ETHUSDT',
                market: MarketType.FUTURE,
                base: 'ETH',
                quote: 'USDT',
                price_dp: 2,
                volume_dp: 4,
                status: PairStatus.INACTIVE,
                default_step: '0.5',
            },
            {
                platform: binance,
                name: 'BTCUSDT',
                market: MarketType.FUTURE,
                base: 'BTC',
                quote: 'USDT',
                price_dp: 2,
                volume_dp: 5,
                status: PairStatus.INACTIVE,
                default_step: '5',
            },
            {
                platform: binance,
                name: 'POLUSDT',
                market: MarketType.FUTURE,
                base: 'POL',
                quote: 'USDT',
                price_dp: 4,
                volume_dp: 1,
                status: PairStatus.INACTIVE,
                default_step: '0.0002',
            },
            {
                platform: binance,
                name: 'DOGEUSDT',
                market: MarketType.FUTURE,
                base: 'DOGE',
                quote: 'USDT',
                price_dp: 5,
                volume_dp: 0,
                status: PairStatus.INACTIVE,
                default_step: '0.00002',
            },
            {
                platform: binance,
                name: 'SOLUSDT',
                market: MarketType.FUTURE,
                base: 'SOL',
                quote: 'USDT',
                price_dp: 3,
                volume_dp: 5,
                status: PairStatus.INACTIVE,
                default_step: '0.01',
            },
            {
                platform: binance,
                name: 'XRPUSDT',
                market: MarketType.FUTURE,
                base: 'XRP',
                quote: 'USDT',
                price_dp: 4,
                volume_dp: 0,
                status: PairStatus.INACTIVE,
                default_step: '0.00001',
            },
        ])
        pinoLogger.info('[SEED] Pair table seeded.')
    }
}