import { BadRequestException, Injectable } from '@nestjs/common';
import { CreateOrderDto } from './dto/create-order.dto';
import { EntityManager, Equal, FindOptionsWhere } from 'typeorm';
import { Pair } from 'src/pair/entities/pair.entity';
import { CustomErrorMessages } from 'common/helper/errorMessage';
import { User } from 'src/user/entities/user.entity';
import { Queue } from 'bullmq';
import { InjectQueue } from '@nestjs/bullmq';
import { Api } from 'src/api/entities/api.entity';
import { WalletService } from 'src/wallet/wallet.service';
import Decimal from 'decimal.js';
import { OrderSide, OrderType } from 'common/interface/order.enum';
import { CancelOrderDto } from './dto/cancel-order.dto';
import { BinanceHelper } from 'common/helper/binance';
import { decrypted } from 'common/util/utils';
import configuration from 'config/configuration';
import { Signal } from 'src/signal/entities/signal.entity';
import { PairStatus } from 'common/interface/pair-status.enum';
import { GetOrderQueryDto } from './dto/get-order-query.dto';
import { UserPair } from 'src/user_pair/entities/user_pair.entity';
import { Order } from './entities/order.entity';
import { MarketType } from 'common/interface/market-type.enum';

@Injectable()
export class OrderService {
  constructor(
    private readonly em: EntityManager,
    private readonly walletService: WalletService,
    @InjectQueue('order-queue') private readonly orderQueue: Queue,
  ) { }

  async queueCreateOrder(createOrderDto: CreateOrderDto, user: User) {
    const [signal, userPair] = await Promise.all([
      this.em.findOneBy(Signal, {
        id: Equal(createOrderDto.signal_id),
        is_active: Equal(true),
      }),
      this.em.findOne(UserPair, {
        where: {
          user: { id: Equal(user.id) },
          pair: {
            id: Equal(createOrderDto.pair_id),
            status: Equal(PairStatus.ACTIVE),
            platform: { id: Equal(1) },
          },
          signal: { id: Equal(createOrderDto.signal_id) },
          is_active: Equal(true),
        },
        relations: ['pair']
      }),
    ])

    if (!signal)
      throw new BadRequestException(CustomErrorMessages.SIGNAL.INVALID_SIGNAL)
    if (!userPair)
      throw new BadRequestException(CustomErrorMessages.USER_PAIR.INVALID_USER_PAIR)
    if (signal.market !== MarketType.SPOT || userPair.pair.market !== MarketType.SPOT)
      throw new BadRequestException("Only spot trading supported for now")

    const api = await this.em.findOne(Api, {
      where: {
        user_id: Equal(user.id),
        platform: { id: Equal(1) },
        is_active: Equal(true),
      },
      relations: ['platform'],
    })

    if (!api)
      throw new BadRequestException(CustomErrorMessages.API.INVALID_API)

    if (createOrderDto.type === OrderType.MARKET) {
      const binanceHelper = new BinanceHelper()
      const orderBook = await binanceHelper.getOrderBook(userPair.pair.name)
      const worstAskPrice = orderBook.asks?.[orderBook.asks.length - 1]?.[0]

      if (!worstAskPrice) {
        throw new BadRequestException(CustomErrorMessages.ORDER.MARKET_PRICE_NOT_FOUND)
      }

      createOrderDto.price = worstAskPrice
    }

    await this.walletService.freezeFunds(
      user.id,
      1,
      createOrderDto.signal_id,
      userPair.pair,
      createOrderDto.side,
      createOrderDto.side === OrderSide.BUY ? Decimal(createOrderDto.qty).mul(Decimal(createOrderDto.price)) : Decimal(createOrderDto.qty)
    )

    await this.orderQueue.add('create-order', {
      createOrderDto,
      pair: userPair.pair,
      api,
      userId: user.id,
    })

    return 'Order queued successfully'
  }

  async cancelOrder(cancelOrderDto: CancelOrderDto, user: User) {
    const [api, pair, order] = await Promise.all([
      this.em.createQueryBuilder(Api, 'a')
        .leftJoinAndSelect('a.platform', 'platform')
        .andWhere('a.platform_id = :platformId', { platformId: 1 })
        .andWhere('a.user_id = :userId', { userId: user.id })
        .andWhere('a.is_active = :isActive', { isActive: true })
        .getOne(),
      this.em.findOneBy(Pair, {
        name: Equal(cancelOrderDto.pair_name),
        platform: { id: Equal(1) },
      }),
      this.em.findOneBy(Order, {
        order_id: Equal(cancelOrderDto.order_id),
        pair_name: Equal(cancelOrderDto.pair_name),
      })
    ])

    if (!api)
      throw new BadRequestException(CustomErrorMessages.API.INVALID_API)
    if (!pair)
      throw new BadRequestException(CustomErrorMessages.PAIR.INVALID_PAIR)
    if (!order)
      throw new BadRequestException(CustomErrorMessages.ORDER.ORDER_NOT_FOUND)
    if (pair.market !== MarketType.SPOT)
      throw new BadRequestException("Only spot trading supported for now")

    const secret = await decrypted(api.secret, configuration().encryption.secret)
    const binanceHelper = new BinanceHelper(api.api, secret)
    await binanceHelper.cancelOrder(cancelOrderDto.pair_name, cancelOrderDto.order_id)

    return 'Order cancelled successfully'
  }

  async getOrders(getOrderQueryDto: GetOrderQueryDto, user: User) {
    const where: FindOptionsWhere<Order> = {
      user: { id: Equal(user.id) },
    }

    if (getOrderQueryDto.pair_name)
      where.pair_name = Equal(getOrderQueryDto.pair_name)
    if (getOrderQueryDto.platform_id)
      where.platform = { id: Equal(getOrderQueryDto.platform_id) }
    if (getOrderQueryDto.signal_id)
      where.signal = { id: Equal(getOrderQueryDto.signal_id) }
    if (getOrderQueryDto.status)
      where.status = Equal(getOrderQueryDto.status)

    return await this.em.find(Order, {
      where,
      relations: ['platform', 'user', 'signal'],
    })
  }
}
