import { Controller, Get, Query } from '@nestjs/common';
import { PlatformService } from './platform.service';
import { ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { GetPlatformQueryDto } from './dto/get-platform-query.dto';

@ApiTags('Platform')
@Controller('platform')
export class PlatformController {
  constructor(
    private readonly platformService: PlatformService
  ) { }

  @Get()
  @ApiOperation({
    summary: 'Get all platforms',
    description: 'Get all platforms with optional filtering by active status',
  })
  @ApiQuery({
    name: 'is_active',
    required: false,
    type: Boolean,
    description: 'Platform active status'
  })
  async find(@Query() getPlatformQueryDto: GetPlatformQueryDto) {
    return await this.platformService.find(getPlatformQueryDto);
  }
}
