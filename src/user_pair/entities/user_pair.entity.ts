import { DecimalJsTransformer } from "common/util/decimal-js-transformer";
import { Pair } from "src/pair/entities/pair.entity";
import { Signal } from "src/signal/entities/signal.entity";
import { User } from "src/user/entities/user.entity";
import { Column, CreateDateColumn, Entity, JoinColumn, ManyToOne, PrimaryGeneratedColumn, Unique, UpdateDateColumn } from "typeorm";

@Entity()
@Unique(['user', 'pair', 'signal'])
export class UserPair {
    @PrimaryGeneratedColumn({ type: 'bigint', unsigned: true })
    id: number;

    @ManyToOne(() => User, { nullable: false })
    @JoinColumn({ name: 'user_id' })
    user: User;

    @ManyToOne(() => Pair, { nullable: false })
    @JoinColumn({ name: 'pair_id' })
    pair: Pair;

    @ManyToOne(() => Signal)
    @JoinColumn({ name: 'signal_id' })
    signal: Signal;

    @Column({ nullable: true, type: 'bigint', unsigned: true })
    last_order_id: number;

    @Column({
        default: '2',
        type: 'decimal',
        precision: 8,
        scale: 2,
        transformer: DecimalJsTransformer,
    })
    gap: string;

    @Column({ default: 2 })
    max_orders: number;

    @Column({
        default: '1',
        type: 'decimal',
        precision: 10,
        scale: 2,
        transformer: DecimalJsTransformer,
    })
    tp: string;

    @Column({
        default: '0.8',
        type: 'decimal',
        precision: 10,
        scale: 2,
        transformer: DecimalJsTransformer,
    })
    sl: string;

    @Column({
        default: '0',
        type: 'decimal',
        precision: 25,
        scale: 8,
        transformer: DecimalJsTransformer,
    })
    qty: string;

    @Column({
        default: '0',
        type: 'decimal',
        precision: 25,
        scale: 8,
        transformer: DecimalJsTransformer,
    })
    step: string;

    @Column({ default: true })
    is_active: boolean;

    @Column({ default: false })
    has_paid: boolean;

    @Column({ default: false })
    auto_entry: boolean;

    @Column({ default: false })
    auto_tp: boolean;

    @Column({ default: false })
    auto_sl: boolean;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;
}
