import { OrderFee } from "src/order_fee/entities/order_fee.entity";
import { Platform } from "src/platform/entities/platform.entity";
import { Signal } from "src/signal/entities/signal.entity";
import { User } from "src/user/entities/user.entity";
import { Column, CreateDateColumn, Entity, JoinColumn, ManyToOne, OneToMany, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";
import { DecimalJsTransformer } from "common/util/decimal-js-transformer";
import { StrategyPrefix } from "common/interface/order.enum";

export enum OrderStatus {
    NEW = 'NEW',
    OPEN = 'OPEN',
    CLOSED = 'CLOSED',
    CANCELLED = 'CANCELLED'
}

@Entity()
export class Order {
    @PrimaryGeneratedColumn({ type: 'bigint', unsigned: true })
    id: number

    @Column({ type: 'bigint', unsigned: true })
    order_id: number

    @ManyToOne(() => Platform, { nullable: false })
    @JoinColumn({ name: 'platform_id' })
    platform: Platform

    @ManyToOne(() => User, { nullable: false })
    @JoinColumn({ name: 'user_id' })
    user: User

    @ManyToOne(() => Signal)
    @JoinColumn({ name: 'signal_id' })
    signal: Signal

    @Column({ unique: true })
    client_order_id: string

    @Column()
    pair_name: string

    @Column({
        default: '0',
        type: 'decimal',
        precision: 25,
        scale: 8,
        transformer: DecimalJsTransformer,
    })
    price: string

    @Column({
        default: '0',
        type: 'decimal',
        precision: 25,
        scale: 8,
        transformer: DecimalJsTransformer,
    })
    qty: string

    @Column({
        default: '0',
        type: 'decimal',
        precision: 25,
        scale: 8,
        transformer: DecimalJsTransformer,
    })
    filled: string

    @Column({
        default: '0',
        type: 'decimal',
        precision: 25,
        scale: 8,
        transformer: DecimalJsTransformer,
    })
    matched: string

    @Column({
        default: '0',
        type: 'decimal',
        precision: 25,
        scale: 8,
        transformer: DecimalJsTransformer,
    })
    amount: string

    @Column({ type: 'enum', enum: OrderStatus, default: OrderStatus.NEW })
    status: OrderStatus

    @Column({ default: true })
    is_buy: boolean

    @Column({ type: 'enum', enum: StrategyPrefix, default: StrategyPrefix.EZBOT })
    strategy: StrategyPrefix

    @Column({ default: false })
    fee_done: boolean

    @CreateDateColumn()
    created_at: Date

    @UpdateDateColumn()
    updated_at: Date

    @OneToMany(() => OrderFee, (orderFee) => orderFee.order)
    orderFees: OrderFee[]
}
