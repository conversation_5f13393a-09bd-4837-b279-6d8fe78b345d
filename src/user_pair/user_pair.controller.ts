import { Body, Controller, Get, Post, Query, Req, UseGuards } from '@nestjs/common';
import { UserPairService } from './user_pair.service';
import { ApiBearerAuth, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/auth/jwt-auth.guard';
import { CreateUserPairDto } from './dto/create-user_pair.dto';
import { GetUserPairQueryDto } from './dto/get-user-pair-query.dto';
import { UpdateUserPairDto } from './dto/update-user_pair.dto';

@ApiTags('User Pair')
@Controller('user-pair')
export class UserPairController {
  constructor(
    private readonly userPairService: UserPairService
  ) {}

  @Post('create')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Create user pair',
    description: 'Creates a user pair configuration'
  })
  async create(@Body() createUserPairDto: CreateUserPairDto, @Req() req: any) {
    return await this.userPairService.create(createUserPairDto, req.user);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Get user pairs',
  })
  @ApiQuery({
    name: 'platform_id',
    required: false,
    type: Number,
    description: 'Platform ID',
  })
  @ApiQuery({
    name: 'signal_id',
    required: false,
    type: Number,
    description: 'Signal ID',
  })
  @ApiQuery({
    name: 'is_active',
    required: false,
    type: Boolean,
    description: 'User pair active status',
  })
  async findAll(@Query() getUserPairQueryDto: GetUserPairQueryDto, @Req() req: any) {
    return await this.userPairService.findAll(getUserPairQueryDto, req.user);
  }

  @Post('update')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Update user pair',
  })
  async update(@Body() updateUserPairDto: UpdateUserPairDto, @Req() req: any) {
    return await this.userPairService.update(updateUserPairDto, req.user);
  }
}
