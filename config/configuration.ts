require('dotenv').config();
export default () => ({
  port: parseInt(process.env.PORT, 10) || 3000,
  redis: {
    host: process.env.REDIS_HOST,
    port: parseInt(process.env.REDIS_PORT, 10) || 6379,
    password: process.env.REDIS_PASSWORD,
  },
  database: {
    type: 'mysql',
    host: process.env.DATABASE_HOST,
    port: parseInt(process.env.DATABASE_PORT),
    username: process.env.DATABASE_USERNAME,
    password: process.env.DATABASE_PASSWORD,
    database: process.env.DATABASE_NAME,
    synchronize: <PERSON><PERSON><PERSON>(process.env.SYNC),
  },
  jwtConstant: {
    jwtAccessSecret: process.env.JWT_ACCESS_SECRET,
    jwtRefreshSecret: process.env.JWT_REFRESH_SECRET,
    jwtAccessExpired: process.env.JWT_ACCESS_EXPIRATION,
    jwtRefreshExpired: process.env.JWT_REFRESH_EXPIRATION
  },
  encryption: {
    secret: process.env.ENCRYPTION_SECRET,
  },
  stream_url: process.env.STREAM_URL,
  email: {
    host: process.env.MAIL_HOST,
    port: parseInt(process.env.MAIL_PORT),
    user: process.env.MAIL_USER,
    password: process.env.MAIL_PASSWORD,
  },
  environment: process.env.NODE_ENV,
  taillog: {
    token: process.env.TAILLOG_TOKEN,
    url: process.env.TAILLOG_URL,
  },
});
