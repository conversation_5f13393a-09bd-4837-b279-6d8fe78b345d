<style>

/* -------------------------------------
    GLOBAL RESETS
------------------------------------- */

/*All the styling goes here*/

.header {
    text-align: center;
    padding: 15px 0;
}

.image {
    max-width: 14rem;
    height: auto;
    padding-top: 3rem;
}

.background-header {
    background: #000;
    width: auto;
    background-image: url('https://ss2murni.oss-ap-southeast-3.aliyuncs.com/0x5021726654403751');
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    text-align: center; 
    height: 150px;
}

/* Styles for samll screens */
@media screen and (min-width: 640px) {
    .background-header {
        background-position: center -1.5rem !important;
    }
}

/* Styles for medium screens */
@media screen and (min-width: 768px) {
    .background-header {
        background-position: center -3rem !important;
    }
}
/* Styles for large screens */
@media screen and (min-width: 1280px) {
    .background-header {
        background-position: center -6rem !important;
    }
}

.footer {
  text-align: center;
  padding: 40px 0 20px 0;
}

.footer {
  text-align: center;
  color: #ffffff;
}

.footer p {
    font-size: 12px;
}

.footer img {
    max-width: 100px;
} 

.background-footer {
    background: #000;
    background-image: url('https://ss2murni.oss-ap-southeast-3.aliyuncs.com/0x5021726654246665');
    background-position: center rem;
    background-size: cover;
    background-repeat: no-repeat;
    text-align: center;
    padding: 20px 0 10px 0;
  }

img {
  border: none;
  -ms-interpolation-mode: bicubic;
  max-width: 100%; 
}

body {
  background-color: #000;
  background-image:linear-gradient(#000,#000);
  font-family: Verdana, sans-serif;
  -webkit-font-smoothing: antialiased;
  font-size: 14px;
  line-height: 1.4;
  margin: 0;
  padding: 0;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%; 
}

table {
  border-collapse: separate;
  mso-table-lspace: 0pt;
  mso-table-rspace: 0pt;
  min-width: 100%;
  width: 100%; }
  table td {
    font-family: Verdana, sans-serif;
    font-size: 14px;
    vertical-align: top; 
}

/* -------------------------------------
    BODY & CONTAINER
------------------------------------- */

.body {
   background-color: #000 !important;
   color: white !important;
   width: 100%; 
}

/* Set a max-width, and make it display as block so it will automatically stretch to that width, but will also shrink down on a phone or something */
.container {
  display: block;
  Margin: 0 auto !important;
  /* makes it centered */
  max-width: 640px;
  width: 580px; 
}

/* This should also be a block element, so that it will fill 100% of the .container */
.content {
  box-sizing: border-box;
  display: block;
  Margin: 0 auto;
  max-width: 640px; 
}

/* -------------------------------------
    HEADER, FOOTER, MAIN
------------------------------------- */
.passwordTable {
   margin: 20px 0px 20px 0px;
   border-radius: 3px;
   border-top-left-radius: 25px;
   border-top-right-radius: 25px;
   border-bottom-left-radius: 25px;
   border-bottom-right-radius: 25px;
   width: 100%; 
}

.passwordTable th {
  padding: 1rem 0 1rem 0;
  font-size: 1.5rem;
  text-align: left;
} 

.passwordTable td {
   text-align: left;
} 

.issuerDataTable {
   padding: 15px 0px 15px 0px;
   border-radius: 3px;
   width: 100%; 
   font-size: 12px;
   text-align: center;
}

.issuerDataTable th {
   text-align: left;
   justify-items: center;
   justify-content: center;
   padding-bottom: 0.5rem;
} 

.issuerDataTable td {
  padding-left: 3rem;
  text-align: left;
  justify-items: center;
  justify-content: center;
  font-size: 12px;
} 

.main {
  background: #000;
  border-radius: 3px;
  width: 100%; 
}

.header {
  padding: 20px 0;
}

.wrapper {
  box-sizing: border-box;
  padding: 0px 20px; 
}

.content-block {
  padding-bottom: 10px;
  padding-top: 10px;
}

.footer {
  clear: both;
  Margin-top: 10px;
  text-align: center;
  width: 100%; 
}
  .footer td,
  .footer p,
  .footer span,
  .footer a {
    color: #ffffff;
    font-size: 12px;
    text-align: center; 
}

/* -------------------------------------
    TYPOGRAPHY
------------------------------------- */
h1,
h2,
h3,
h4 {
  color: white;
  font-family: Verdana, sans-serif;
  font-weight: 400;
  line-height: 1.4;
  margin: 0;
  margin-bottom: 30px; 
}

h1 {
  font-size: 35px;
  font-weight: 300;
  text-align: center;
  text-transform: capitalize; 
}

p,
ul,
ol {
  font-family: Verdana, sans-serif;
  font-size: 14px;
  font-weight: normal;
  margin: 0;
  margin-bottom: 12px; 
}
  p li,
  ul li,
  ol li {
    list-style-position: inside;
    margin-left: 5px; 
}

a {
  color: white;
  text-decoration: underline; 
}

/* -------------------------------------
    BUTTONS
------------------------------------- */
.btn {
  box-sizing: border-box;
  width: 100%; }
  .btn > tbody > tr > td {
    padding-bottom: 15px; }
  .btn table {
    min-width: auto;
    width: auto; 
}
  .btn table td {
    background-color: #000;
    border-radius: 5px;
    text-align: center; 
}
  .btn a {
    background-color: black;
    border: solid 1px black;
    border-radius: 5px;
    box-sizing: border-box;
    color: #ffffff;
    cursor: pointer;
    display: inline-block;
    font-size: 14px;
    font-weight: bold;
    margin: 0;
    padding: 12px 25px;
    text-decoration: none;
    text-transform: capitalize; 
}

.btn-primary table td {
  background-color: #ec0867; 
}

.btn-primary a {
  background-color: #ec0867;
  border-color: #ec0867;
  color: #ffffff; 
}

/* -------------------------------------
    OTHER STYLES THAT MIGHT BE USEFUL
------------------------------------- */
.last {
  margin-bottom: 0; 
}

.first {
  margin-top: 0; 
}

.align-center {
  text-align: center; 
}

.align-right {
  text-align: right; 
}

.align-left {
  text-align: left; 
}

.clear {
  clear: both; 
}

.mt0 {
  margin-top: 0; 
}

.mb0 {
  margin-bottom: 0; 
}

.preheader {
  color: transparent;
  display: none;
  height: 0;
  max-height: 0;
  max-width: 0;
  opacity: 0;
  overflow: hidden;
  mso-hide: all;
  visibility: hidden;
  width: 0; 
}

.powered-by a {
  text-decoration: none; 
}

hr {
  border: 0;
  border-bottom: 1px solid #f6f6f6;
  Margin: 10px 0; 
}

/* -------------------------------------
    RESPONSIVE AND MOBILE FRIENDLY STYLES
------------------------------------- */
@media only screen and (max-width: 620px) {
  table[class=body] h1 {
    font-size: 28px !important;
    margin-bottom: 10px !important; 
  }
  table[class=body] p,
  table[class=body] ul,
  table[class=body] ol,
  table[class=body] td,
  table[class=body] span,
  table[class=body] a {
    font-size: 16px !important; 
  }
  table[class=body] .wrapper,
  table[class=body] .article {
    padding: 10px !important; 
  }
  table[class=body] .content {
    padding: 0 !important; 
  }
  table[class=body] .container {
    padding: 0 !important;
    width: 100% !important; 
  }
  table[class=body] .main {
    border-left-width: 0 !important;
    border-radius: 0 !important;
    border-right-width: 0 !important; 
  }
  table[class=body] .btn table {
    width: 100% !important; 
  }
  table[class=body] .btn a {
    width: 100% !important; 
  }
  table[class=body] .img-responsive {
    height: auto !important;
    max-width: 100% !important;
    width: auto !important; 
  }
}

/* -------------------------------------
    PRESERVE THESE STYLES IN THE HEAD
------------------------------------- */
@media all {
  .ExternalClass {
    width: 100%; 
  }
  .ExternalClass,
  .ExternalClass p,
  .ExternalClass span,
  .ExternalClass font,
  .ExternalClass td,
  .ExternalClass div {
    line-height: 100%; 
  }
  .apple-link a {
    color: inherit !important;
    font-family: inherit !important;
    font-size: inherit !important;
    font-weight: inherit !important;
    line-height: inherit !important;
    text-decoration: none !important; 
  }
  .btn-primary table td:hover {
    background-color: #d5075d !important; 
  }
  .btn-primary a:hover {
    background-color: #d5075d !important;
    border-color: #d5075d !important; 
  } 
}

</style>

<div data-template-type="html" style="height: auto;" class="ui-sortable">
   <!-- ====== Module : Intro ====== -->
    <table role="presentation" border="0" cellpadding="0" cellspacing="0">
      <tr>
        <td class="background-header">
            <a href="https://ti-staging.secondswap.io/">
                <img class="image" src="http://crazydog.oss-ap-southeast-1.aliyuncs.com/0xdbb1724227594511" alt="SecondSwap">
            </a>
        </td>
      </tr>
    </table>
    <table role="presentation" border="0" cellpadding="0" cellspacing="0" class="body">
      <tr>
        <td class="container">
          <div class="content">

            <!-- START CENTERED WHITE CONTAINER -->
            <table role="presentation" class="main">

              <!-- START MAIN CONTENT AREA -->
              <tr>
                <td class="wrapper">
                  <table role="presentation" border="0" cellpadding="0" cellspacing="0" style="color: white;">
                    <tr>
                      <td>
                        <p style="color: white; padding-bottom: 0.5rem;">Dear User, </p>
                        <p style="color: white;">Please enter the 6-digit OTP below to login to token issuer dashboard.</p>

                        <table class="passwordTable" style="color: white;">
                          <tr>
                              <th>{{otp}}</th>
                          </tr>
                        </table>
                        <p style="padding-bottom:30px; font-size: 12px; color: white;">
                          ** This OTP will be valid for 1 minute.
                        </p>
                        <hr>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>

            <!-- END MAIN CONTENT AREA -->
            </table>
          <!-- END CENTERED WHITE CONTAINER -->
          </div>
        </td>
      </tr>
    </table>
    <!-- START FOOTER -->
    <div class="background-footer">
        <div class="footer">
            <p>This is an auto-generated message. Please do not reply.</p>
        </div>
    </div>
    <!-- END FOOTER -->
</div>