import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, IsString } from 'class-validator';

export class CreateApiDto {
    @ApiProperty({ description: 'api key' })
    @IsNotEmpty()
    @IsString()
    api: string;

    @ApiProperty({ description: 'api secret'})
    @IsNotEmpty()
    @IsString()
    secret: string;

    @ApiProperty({ description: 'platform id' })
    @IsNotEmpty()
    @IsInt()
    platform_id: number;
}
