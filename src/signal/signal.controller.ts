import { Controller, Get, Query } from '@nestjs/common';
import { SignalService } from './signal.service';
import { ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { GetSignalsQueryDto } from './dto/get-signals-query.dto';
import { MarketType } from 'common/interface/market-type.enum';

@ApiTags('Signal')
@Controller('signal')
export class SignalController {
  constructor(
    private readonly signalService: SignalService
  ) { }

  @Get()
  @ApiOperation({
    summary: 'Get signals',
    description: 'Get all trading signals with optional filtering by market type and active status',
  })
  @ApiQuery({
    name: 'market',
    required: false,
    enum: MarketType,
    description: 'Market type',
  })
  @ApiQuery({
    name: 'is_active',
    required: false,
    type: Boolean,
    description: 'Signal active status',
  })
  async find(@Query() getSignalsQueryDto: GetSignalsQueryDto) {
    return await this.signalService.find(getSignalsQueryDto);
  }
}
