import { ApiProperty } from "@nestjs/swagger";
import { IsEmail, IsInt, IsNotEmpty, IsNumberString, IsString, Min } from "class-validator";

export class CreateSubAccDto {
    @ApiProperty({ description: 'Platform ID', example: 1 })
    @IsNotEmpty()
    @IsInt()
    @Min(1)
    platform_id: number;
    
    @ApiProperty({ description: 'Signal ID', example: 17 })
    @IsNotEmpty()
    @IsInt()
    signal_id: number

    @ApiProperty({ description: 'Sub user email', example: '<EMAIL>' })
    @IsNotEmpty()
    @IsEmail()
    email: string;

    @ApiProperty({ description: 'Sub user password', example: 'abc123' })
    @IsNotEmpty()
    @IsString()
    password: string;

    @ApiProperty({ description: 'Sub user name', example: 'KK' })
    @IsNotEmpty()
    @IsString()
    name: string;

    @ApiProperty({ description: 'Allocation coin', example: 'FDUSD' })
    @IsNotEmpty()
    @IsString()
    allocation_coin: string;

    @ApiProperty({ description: 'Allocation quantity', example: '5000' })
    @IsNotEmpty()
    @IsNumberString()
    allocation_qty: string;
}