import { BadRequestException, Injectable } from '@nestjs/common';
import { <PERSON>tity<PERSON>anager, Equal } from 'typeorm';
import { CreateUserPairDto } from './dto/create-user_pair.dto';
import { User } from 'src/user/entities/user.entity';
import { Pair } from 'src/pair/entities/pair.entity';
import { Signal } from 'src/signal/entities/signal.entity';
import { CustomErrorMessages } from 'common/helper/errorMessage';
import { UserPair } from './entities/user_pair.entity';
import { PairStatus } from 'common/interface/pair-status.enum';
import { GetUserPairQueryDto } from './dto/get-user-pair-query.dto';
import { Platform } from 'src/platform/entities/platform.entity';
import { UpdateUserPairDto } from './dto/update-user_pair.dto';

@Injectable()
export class UserPairService {
  constructor(
    private readonly em: EntityManager,
  ) { }

  async create(createUserPairDto: CreateUserPairDto, user: User) {
    const pair = await this.em.findOneBy(Pair, {
      id: Equal(createUserPairDto.pair_id),
    });

    if (!pair) {
      throw new BadRequestException(CustomErrorMessages.PAIR.PAIR_NOT_FOUND);
    }

    if (pair.status != PairStatus.ACTIVE) {
      throw new BadRequestException(CustomErrorMessages.PAIR.PAIR_NOT_ACTIVE);
    }

    const signal = await this.em.findOneBy(Signal, {
      id: Equal(createUserPairDto.signal_id),
    });

    if (!signal) {
      throw new BadRequestException(CustomErrorMessages.SIGNAL.SIGNAL_NOT_FOUND(createUserPairDto.signal_id));
    }

    if (!signal.is_active) {
      throw new BadRequestException(CustomErrorMessages.SIGNAL.SIGNAL_NOT_ACTIVE);
    }

    if (pair.market !== signal.market) {
      throw new BadRequestException(CustomErrorMessages.PAIR.PAIR_MARKET_NOT_MATCH);
    }

    const existingUserPair = await this.em.createQueryBuilder(UserPair, 'userPair')
      .where('userPair.user_id = :userId', { userId: user.id })
      .andWhere('userPair.pair_id = :pairId', { pairId: pair.id })
      .andWhere('userPair.signal_id = :signalId', { signalId: signal.id })
      .getOne();

    if (existingUserPair) {
      throw new BadRequestException(CustomErrorMessages.USER_PAIR.USER_PAIR_EXIST);
    }

    await this.em.save(UserPair, {
      ...createUserPairDto,
      step: pair.default_step,
      user: user,
      pair: pair,
      signal: signal,
    });

    return `User pair created successfully`;
  }

  async findAll(getUserPairQueryDto: GetUserPairQueryDto, user: User) {
    if (getUserPairQueryDto.platform_id) {
      const platform = await this.em.findOneBy(Platform, {
        id: Equal(getUserPairQueryDto.platform_id),
      });

      if (!platform) {
        throw new BadRequestException(CustomErrorMessages.PLATFORM.NOT_FOUND_PLATFORM);
      }
    }

    if (getUserPairQueryDto.signal_id) {
      const signal = await this.em.findOneBy(Signal, {
        id: Equal(getUserPairQueryDto.signal_id),
      });

      if (!signal) {
        throw new BadRequestException(CustomErrorMessages.SIGNAL.SIGNAL_NOT_FOUND());
      }
    }

    const query = this.em.createQueryBuilder(UserPair, 'user_pair')
      .leftJoinAndSelect('user_pair.pair', 'pair')
      .leftJoinAndSelect('pair.platform', 'platform')
      .leftJoinAndSelect('user_pair.signal', 'signal')
      .where('user_pair.user_id = :userId', { userId: user.id });

    if (getUserPairQueryDto.platform_id) {
      query.andWhere('platform.id = :platformId', { platformId: getUserPairQueryDto.platform_id });
    }

    if (getUserPairQueryDto.signal_id) {
      query.andWhere('user_pair.signal_id = :signalId', { signalId: getUserPairQueryDto.signal_id });
    }

    if (getUserPairQueryDto.is_active !== undefined) {
      query.andWhere('user_pair.is_active = :isActive', { isActive: getUserPairQueryDto.is_active });
    }

    const userPairs = await query.getMany()

    return userPairs
  }

  async update(updateUserPairDto: UpdateUserPairDto, user: User) {
    const [userPair, pair, signal] = await Promise.all([
      this.em.createQueryBuilder(UserPair, 'user_pair')
        .leftJoinAndSelect('user_pair.user', 'user')
        .leftJoinAndSelect('user_pair.pair', 'pair')
        .leftJoinAndSelect('user_pair.signal', 'signal')
        .where('user_pair.id = :userPairId', { userPairId: updateUserPairDto.id })
        .andWhere('user_pair.user_id = :userId', { userId: user.id })
        .getOne(),
      this.em.findOneBy(Pair, {
        id: Equal(updateUserPairDto.pair_id),
      }),
      this.em.findOneBy(Signal, {
        id: Equal(updateUserPairDto.signal_id),
      }),
    ]);

    if (!userPair) {
      throw new BadRequestException(CustomErrorMessages.USER_PAIR.USER_PAIR_NOT_FOUND);
    }

    if (!pair) {
      throw new BadRequestException(CustomErrorMessages.PAIR.PAIR_NOT_FOUND);
    }
    if (pair.status !== PairStatus.ACTIVE) {
      throw new BadRequestException(CustomErrorMessages.PAIR.PAIR_NOT_ACTIVE);
    }

    if (!signal) {
      throw new BadRequestException(CustomErrorMessages.SIGNAL.SIGNAL_NOT_FOUND(updateUserPairDto.signal_id));
    }
    if (!signal.is_active) {
      throw new BadRequestException(CustomErrorMessages.SIGNAL.SIGNAL_NOT_ACTIVE);
    }

    const existingUserPair = await this.em.createQueryBuilder(UserPair, 'userPair')
      .where('userPair.user_id = :userId', { userId: user.id })
      .andWhere('userPair.id != :currentId', { currentId: updateUserPairDto.id })
      .andWhere('userPair.pair_id = :pairId', { pairId: updateUserPairDto.pair_id })
      .andWhere('userPair.signal_id = :signalId', { signalId: updateUserPairDto.signal_id })
      .getOne();

    if (existingUserPair) {
      throw new BadRequestException(CustomErrorMessages.USER_PAIR.USER_PAIR_EXIST);
    }

    if (pair.market !== signal.market) {
      throw new BadRequestException(CustomErrorMessages.PAIR.PAIR_MARKET_NOT_MATCH);
    }

    const { id, ...updateUserPairData } = updateUserPairDto;
    const updatedUserPair = this.em.merge(UserPair, userPair, {
      ...updateUserPairData,
      pair,
      signal,
    });
    await this.em.save(updatedUserPair);

    return 'User pair updated successfully';
  }
}
