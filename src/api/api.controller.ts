import { Controller, Get, Post, Body, UseGuards, Req, Query } from '@nestjs/common';
import { ApiService } from './api.service';
import { CreateApiDto } from './dto/create-api.dto';
import { UpdateApiDto } from './dto/update-api.dto';
import { ApiBearerAuth, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/auth/jwt-auth.guard';
import { GetApiQueryDto } from './dto/get-api-query.dto';

@Controller('api')
@ApiTags('Api')
export class ApiController {
  constructor(
    private readonly apiService: ApiService
  ) { }

  @Post('create')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Create API key (Main user only)',
  })
  async create(@Body() createApiDto: CreateApiDto, @Req() req) {
    return await this.apiService.create(createApiDto, req.user);
  }

  @Get('get')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Get API key (Main user only)',
    description: 'Get API key with optional filtering by platform_id',
  })
  @ApiQuery({
    name: 'platform_id',
    required: false,
    description: 'Platform ID',
  })
  async find(@Query() getApiQueryDto: GetApiQueryDto, @Req() req) {
    return await this.apiService.find(getApiQueryDto, req.user);
  }

  @Post('update')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Update API key (Main user only)',
  })
  async update(@Body() updateApiDto: UpdateApiDto, @Req() req: any) {
    return await this.apiService.update(updateApiDto, req.user);
  }
}
