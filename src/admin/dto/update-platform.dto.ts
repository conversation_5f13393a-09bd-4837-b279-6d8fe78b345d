import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsInt, IsNotEmpty, IsString } from 'class-validator';

export class UpdatePlatformDto {
    @ApiProperty({ description: 'Platform ID', example: 1 })
    @IsNotEmpty()
    @IsInt()
    id: number;

    @ApiProperty({ description: 'Platform name', example: 'Binance' })
    @IsNotEmpty()
    @IsString()
    name: string

    @ApiProperty({ description: 'Platform active status', example: true })
    @IsNotEmpty()
    @IsBoolean()
    is_active: boolean
}
