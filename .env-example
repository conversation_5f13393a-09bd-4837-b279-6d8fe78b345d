# Server Configuration
PORT=3000
SOCKET_PORT=

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
# REDIS_PASSWORD=your_redis_password

# MySQL Database Configuration
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_USERNAME=
DATABASE_PASSWORD=
DATABASE_NAME=
SYNC=false

# Authentication and Security
JWT_ACCESS_SECRET=your-jwt-access-secret-key
JWT_REFRESH_SECRET=your-jwt-refresh-secret-key
JWT_ACCESS_EXPIRATION=900s
JWT_REFRESH_EXPIRATION=7d
ENCRYPTION_SECRET=your-encryption-secret-key

# Streaming Configuration
STREAM_URL=your_stream_url

# Email Configuration
MAIL_HOST=smtp.googlemail.com
MAIL_PORT=465
MAIL_USER=
MAIL_PASSWORD=

# Environment
NODE_ENV=DEVELOPMENT

# Logtail Configuration
TAILLOG_TOKEN=
TAILLOG_URL=