import { Modu<PERSON> } from '@nestjs/common';
import { OrderService } from './order.service';
import { OrderController } from './order.controller';
import { BullModule } from '@nestjs/bull';
import configuration from 'config/configuration';
import { QueueModule } from 'src/queue/queue.module';
import { WalletModule } from 'src/wallet/wallet.module';

@Module({
  imports: [
    QueueModule,
    WalletModule,
    BullModule.registerQueue({
      name: 'order-queue',
      redis: {
        host: configuration().redis.host,
        port: configuration().redis.port,
        password: configuration().redis.password,
      },
      defaultJobOptions: {
        removeOnComplete: 100,
        removeOnFail: 50,
        attempts: 5,
        delay: 1000,
      },
    })
  ],
  controllers: [OrderController],
  providers: [OrderService],
})
export class OrderModule { }
