import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsBoolean, IsNotEmpty, IsOptional, Min, IsNumberString, IsInt } from "class-validator";

export class CreateUserPairDto {
    @ApiProperty({ description: 'Pair ID', example: 1 })
    @IsNotEmpty()
    @IsInt()
    @Min(1)
    pair_id: number;

    @ApiProperty({ description: 'Signal ID', example: 1 })
    @IsNotEmpty()
    @IsInt()
    @Min(1)
    signal_id: number;

    @ApiProperty({ description: 'Order quantity', example: '0.1' })
    @IsNotEmpty()
    @IsNumberString()
    qty: string;

    @ApiPropertyOptional({ description: 'Whether to enable automatic entry', example: false })
    @IsOptional()
    @IsBoolean()
    auto_entry?: boolean;

    @ApiPropertyOptional({ description: 'Gap percentage between orders (used with auto-entry)', example: '2.0' })
    @IsOptional()
    @IsNumberString()
    gap?: string;

    @ApiPropertyOptional({ description: 'Maximum number of orders (used with auto-entry)', example: 3 })
    @IsOptional()
    @IsInt()
    max_orders?: number;

    @ApiPropertyOptional({ description: 'Whether to enable automatic take profit', example: false })
    @IsOptional()
    @IsBoolean()
    auto_tp?: boolean;

    @ApiPropertyOptional({ description: 'Take profit percentage', example: '2.0' })
    @IsOptional()
    @IsNumberString()
    tp?: string;

    @ApiPropertyOptional({ description: 'Whether to enable automatic stop loss', example: false })
    @IsOptional()
    @IsBoolean()
    auto_sl?: boolean;

    @ApiPropertyOptional({ description: 'Stop loss percentage', example: '1.8' })
    @IsOptional()
    @IsNumberString()
    sl?: string;
}
