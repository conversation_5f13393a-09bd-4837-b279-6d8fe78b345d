import { Exclude } from 'class-transformer';
import { Platform } from 'src/platform/entities/platform.entity';
import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';

@Entity()
export class Api {
  @PrimaryGeneratedColumn({ type: 'bigint', unsigned: true })
  id: number;

  @ManyToOne(() => Platform, { nullable: false })
  @JoinColumn({ name: 'platform_id' })
  platform: Platform;

  @Column({ type: 'bigint', unsigned: true })
  user_id: number;

  @Column()
  api: string;

  @Column({ type: "varchar", length: 511 })
  secret: string;

  @Column({ type: 'bigint', unsigned: true, nullable: true })
  main_user_id: number;

  @Column({ nullable: true, default: '' })
  @Exclude()
  listen_key: string;

  @Column({ nullable: true, default: '' })
  @Exclude()
  future_key: string;

  @Column({ default: 1 })
  is_active: boolean;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
