import { Module } from '@nestjs/common';
import { UserService } from './user.service';
import { UserController } from './user.controller';
import configuration from 'config/configuration';
import { JwtModule } from '@nestjs/jwt';
import { AuthModule } from 'src/auth/auth.module';
import { BullModule } from '@nestjs/bull';
import { WalletModule } from 'src/wallet/wallet.module';

@Module({
  imports: [
    JwtModule.register({
      secret: configuration().jwtConstant.jwtAccessSecret,
      signOptions: { expiresIn: configuration().jwtConstant.jwtAccessExpired },
    }),
    AuthModule,
    BullModule.registerQueue({
      name: 'email',
      redis: {
        host: configuration().redis.host,
        port: configuration().redis.port,
        password: configuration().redis.password,
      },
    }),
    WalletModule,
  ],

  providers: [UserService],
  controllers: [UserController],
})
export class UserModule { }
