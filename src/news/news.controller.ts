import { Controller, Get, Post, Body, Query } from '@nestjs/common';
import { NewsService } from './news.service';
import { CreateNewsDto } from './dto/create-news.dto';
import { FilterNewsDto } from './dto/filter-news.dto';
import { ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';

@ApiTags('News')
@Controller('news')
export class NewsController {
  constructor(
    private readonly newsService: NewsService
  ) { }

  // @Post('create')
  // @ApiOperation({
  //   summary: 'Create news',
  // })
  // async create(@Body() createNewsDto: CreateNewsDto) {
  //   return await this.newsService.create(createNewsDto);
  // }

  @Get()
  @ApiOperation({
    summary: 'Get news',
    description: 'Get news with optional filtering by source and keyword',
  })
  @ApiQuery({
    name: 'source',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'keyword',
    required: false,
    type: String,
  })
  async filterNews(@Query() filterNewsDto: FilterNewsDto) {
    return await this.newsService.filterNews(filterNewsDto);
  }
}
