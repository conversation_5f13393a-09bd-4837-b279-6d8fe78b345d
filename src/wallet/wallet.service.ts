import { BadRequestException, Injectable } from '@nestjs/common';
import { CustomErrorMessages } from 'common/helper/errorMessage';
import { EntityManager, Equal } from 'typeorm';
import { Wallet } from './entities/wallet.entity';
import Decimal from 'decimal.js';
import { TransactionAction, TransactionLog } from 'common/entity/transaction-log.entity';
import { Pair } from 'src/pair/entities/pair.entity';
import { OrderSide } from 'common/interface/order.enum';
import { Api } from 'src/api/entities/api.entity';
import { BinanceHelper } from 'common/helper/binance';
import { decrypted } from 'common/util/utils';
import configuration from 'config/configuration';

@Injectable()
export class WalletService {
    constructor(private readonly em: EntityManager) { }

    private async getBinanceAvailableAsset(asset: string, api: Api) {
        // Get a coin's spot's free balance + flexible earn balance from Binance
        const binanceHelper = new BinanceHelper(api.api, await decrypted(api.secret, configuration().encryption.secret))
        const [spot, earn] = await Promise.all([
            binanceHelper.getSpotBalance(asset),
            binanceHelper.getFlexibleEarnBalance(asset),
        ])
        return Decimal(spot[0]?.free || 0).add(Decimal(earn.rows[0]?.totalAmount || 0))
    }

    async getOrCreateWallet(
        userId: number,
        platformId: number,
        signalId: number,
        coin: string,
        em: EntityManager = this.em
    ): Promise<Wallet> {
        let wallet = await em.findOneBy(Wallet, {
            user_id: Equal(userId),
            platform_id: Equal(platformId),
            signal_id: Equal(signalId),
            coin: Equal(coin),
        })

        if (!wallet) {
            wallet = await em.save(Wallet, {
                user_id: userId,
                platform_id: platformId,
                signal_id: signalId,
                coin,
                qty: '0',
                qty_frozen: '0',
            })
        }

        return wallet
    }

    async assertSufficientBalance(
        userId: number,
        platformId: number,
        signalId: number,
        coin: string,
        qty: Decimal,
        em: EntityManager = this.em
    ): Promise<Wallet> {
        const wallet = await this.getOrCreateWallet(userId, platformId, signalId, coin, em)

        if ((Decimal(wallet.qty).minus(Decimal(wallet.qty_frozen))).lt(qty)) {
            throw new BadRequestException(CustomErrorMessages.WALLET.INSUFFICIENT_BALANCE(coin))
        }

        return wallet
    }

    async assertSufficientFrozenBalance(userId: number, platformId: number, signalId: number, coin: string, qty: Decimal): Promise<Wallet> {
        const wallet = await this.getOrCreateWallet(userId, platformId, signalId, coin)

        if (Decimal(wallet.qty_frozen).lt(qty)) {
            throw new BadRequestException(CustomErrorMessages.WALLET.INSUFFICIENT_FROZEN_BALANCE(coin))
        }

        return wallet
    }

    async manageFunds(
        platformId: number,
        signalId: number,
        coin: string,
        qty: Decimal,
        api: Api,
        subUserId?: number,
        reason?: string,
        em: EntityManager = this.em
    ) {
        const userId = subUserId || api.user_id

        if (qty.lt(0)) {
            // Deallocate funds
            const wallet = await this.assertSufficientBalance(userId, platformId, signalId, coin, qty, em)
            wallet.qty = Decimal(wallet.qty).minus(qty).toString()
            await em.save(Wallet, wallet)
            await this.logTransaction(wallet.id, qty, TransactionAction.DEALLOCATE, reason, null, em)
        } else {
            // Allocate funds
            const wallet = await this.getOrCreateWallet(userId, platformId, signalId, coin, em)

            const { totalAllocatedQty } = await em.createQueryBuilder(Wallet, 'w')
                .leftJoin('api', 'a', 'w.user_id = a.user_id')
                .select('SUM(qty + qty_frozen)', 'totalAllocatedQty')
                .where('w.coin = :coin', { coin })
                .andWhere('(a.main_user_id = :userId OR a.user_id = :userId)', { userId: api.user_id })
                .getRawOne() || { totalAllocatedQty: 0 }

            const binanceAsset = await this.getBinanceAvailableAsset(coin, api)

            if (qty.add(totalAllocatedQty).lte(binanceAsset)) {
                wallet.qty = Decimal(wallet.qty).plus(qty).toString()
                await em.save(Wallet, wallet)
                await this.logTransaction(wallet.id, qty, TransactionAction.ALLOCATE, reason, null, em)
            } else {
                throw new BadRequestException(CustomErrorMessages.WALLET.INSUFFICIENT_BINANCE_BALANCE(coin))
            }
        }
    }

    async freezeFunds(
        userId: number,
        platformId: number,
        signalId: number,
        pair: Pair,
        side: OrderSide,
        orderQty: Decimal,
    ) {
        const base = pair.base
        const quote = pair.quote

        // Determine which coin need to be freezed based on the order side
        const spendCoin = side === OrderSide.BUY ? quote : base

        // Ensure user has sufficient available balance
        const wallet = await this.assertSufficientBalance(userId, platformId, signalId, spendCoin, orderQty)

        // Freeze the specified amount
        wallet.qty_frozen = Decimal(wallet.qty_frozen).plus(orderQty).toString()

        await this.em.save(Wallet, wallet)
        await this.logTransaction(wallet.id, orderQty, TransactionAction.FREEZE, `${side} ${pair.name}`)
    }

    async unfreezeFunds(
        userId: number,
        platformId: number,
        signalId: number,
        coin: string,
        qty: Decimal,
        reason: string,
        clientOrderId?: string
    ) {
        const wallet = await this.assertSufficientFrozenBalance(userId, platformId, signalId, coin, qty)

        wallet.qty_frozen = Decimal(wallet.qty_frozen).minus(qty).toString()

        await this.em.save(Wallet, wallet)
        await this.logTransaction(wallet.id, qty, TransactionAction.UNFREEZE, reason, clientOrderId)
    }

    async handlePostTrade(
        userId: number,
        platformId: number,
        signalId: number,
        spendCoin: string,
        receiveCoin: string,
        frozenQty: Decimal,     // Total amount frozen before placing the order
        spentQty: Decimal,      // Actual amount spent
        receivedQty: Decimal,   // Amount of asset received
        clientOrderId: string
    ) {
        // Calculate the unused portion of the frozen amount
        const unspentQty = frozenQty.minus(spentQty)

        // 1. Deduct the spent amount from the spend wallet
        const spendWallet = await this.assertSufficientFrozenBalance(userId, platformId, signalId, spendCoin, frozenQty)

        spendWallet.qty = Decimal(spendWallet.qty).minus(spentQty).toString()
        spendWallet.qty_frozen = Decimal(spendWallet.qty_frozen).minus(spentQty).toString()

        await this.em.save(Wallet, spendWallet)
        await this.logTransaction(spendWallet.id, spentQty, TransactionAction.SELL, spendCoin, clientOrderId)

        // 3. Unfreeze any unspent (remaining) frozen amount
        if (unspentQty.gt(0)) {
            spendWallet.qty_frozen = Decimal(spendWallet.qty_frozen).minus(unspentQty).toString()

            await this.em.save(Wallet, spendWallet)
            await this.logTransaction(spendWallet.id, unspentQty, TransactionAction.UNFREEZE, 'Refund', clientOrderId)
        }

        // 3. Credit the received asset to the receive wallet
        const receiveWallet = await this.getOrCreateWallet(userId, platformId, signalId, receiveCoin)

        receiveWallet.qty = Decimal(receiveWallet.qty).plus(receivedQty).toString()

        await this.em.save(Wallet, receiveWallet)
        await this.logTransaction(receiveWallet.id, receivedQty, TransactionAction.BUY, receiveCoin, clientOrderId)
    }

    private async logTransaction(
        walletId: number,
        qty: Decimal,
        action: TransactionAction,
        reason: string = null,
        clientOrderId?: string,
        em: EntityManager = this.em
    ) {
        await em.save(TransactionLog, {
            wallet_id: walletId,
            qty: qty.toString(),
            action,
            reason,
            client_order_id: clientOrderId,
        })
    }
}
