import {
  BadRequestException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { AdminLoginDto } from './dto/admin-login.dto';
import { Entity<PERSON>anager, Equal, IsNull } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import configuration from 'config/configuration';
import * as bcrypt from 'bcrypt';
import { Admin } from './entities/admin.entity';
import { AdminChangePasswordDto } from './dto/change-password.dto';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { AdminResetPasswordDto } from './dto/reset-password.dto';
import { decrypted, generateSecret } from 'common/util/utils';
import { CustomErrorMessages } from 'common/helper/errorMessage';
import { Api } from 'src/api/entities/api.entity';
import { CreatePlatformDto } from './dto/create-platform.dto';
import { Platform } from 'src/platform/entities/platform.entity';
import { Signal } from 'src/signal/entities/signal.entity';
import { UpdatePlatformDto } from './dto/update-platform.dto';
import { CreateSignalDto } from './dto/create-signal.dto';
import { UpdateSignalDto } from './dto/update-signal.dto';
import { CreatePairDto } from './dto/create-pair.dto';
import { Pair } from 'src/pair/entities/pair.entity';
import { UpdatePairDto } from './dto/update-pair.dto';
import { RefreshTokenDto } from 'common/dto/refresh-token.dto';
import { BlacklistedToken } from 'common/entity/blacklisted-token.entity';
import { BinanceHelper } from 'common/helper/binance';
import { ToggleApiDto } from './dto/toggle-api.dto';
import { DatastreamBinanceService } from 'src/datastream/datastream.binance.service';
import { PinoLogger } from 'nestjs-pino';

@Injectable()
export class AdminService {
  constructor(
    private readonly em: EntityManager,
    private readonly jwtService: JwtService,
    @InjectQueue('email') private readonly emailQueue: Queue,
    private readonly datastreamBinanceService: DatastreamBinanceService,
    private readonly pinoLogger: PinoLogger,
  ) { }

  async login(adminLoginDto: AdminLoginDto) {
    let admin = await this.em.findOneBy(Admin, {
      email: Equal(adminLoginDto.email),
    });

    if (!admin) {
      throw new BadRequestException(CustomErrorMessages.USER.USER_NOT_FOUND);
    }

    let verify;

    if (admin.temp_password) {
      verify = await bcrypt.compare(
        adminLoginDto.password,
        admin.temp_password,
      );
      if (!verify) {
        verify = await bcrypt.compare(adminLoginDto.password, admin.password);
      } else {
        admin.password = admin.temp_password;
      }
    } else {
      verify = await bcrypt.compare(adminLoginDto.password, admin.password);
    }

    if (!verify) {
      throw new BadRequestException(CustomErrorMessages.USER.INVALID_CREDENTIAL);
    }

    let accessToken = this.jwtService.sign(
      { admin: admin },
      { secret: configuration().jwtConstant.jwtAccessSecret, expiresIn: configuration().jwtConstant.jwtAccessExpired }
    )

    let refreshToken = this.jwtService.sign(
      { admin: admin },
      { secret: configuration().jwtConstant.jwtRefreshSecret, expiresIn: configuration().jwtConstant.jwtRefreshExpired }
    )

    admin.temp_password = null;
    await this.em.save(Admin, admin);

    return { accessToken, refreshToken };
  }

  async logout(req: Request) {
    try {
      let blacklist = new BlacklistedToken()
      blacklist.access_token = await bcrypt.hash(req.headers['authorization']?.replace('Bearer ', ''), 10)
      blacklist.refresh_token = await bcrypt.hash(req.headers['refreshtoken']?.replace('Bearer ', ''), 10)
      await this.em.save(blacklist)
    } catch (error) {
      throw new BadRequestException(error)
    }
    return 'Admin logout successful'
  }

  async refreshToken(refreshTokenDto: RefreshTokenDto) {
    const refreshToken = refreshTokenDto.refresh_token?.replace(/^Bearer\s+/, '')

    try {
      let payload = await this.jwtService.verify(
        refreshToken,
        { secret: configuration().jwtConstant.jwtRefreshSecret }
      )

      let user = await this.em.findOneBy(Admin, {
        email: Equal(payload.admin.email),
      });

      if (!user) throw new UnauthorizedException(CustomErrorMessages.USER.USER_NOT_FOUND)

      let newAccessToken = this.jwtService.sign(
        { admin: payload.admin },
        { secret: configuration().jwtConstant.jwtAccessSecret, expiresIn: configuration().jwtConstant.jwtAccessExpired }
      )

      let newRefreshToken = this.jwtService.sign(
        { admin: payload.admin },
        { secret: configuration().jwtConstant.jwtRefreshSecret, expiresIn: configuration().jwtConstant.jwtRefreshExpired }
      )

      return { access_token: newAccessToken, refresh_token: newRefreshToken }
    } catch (err) {
      if (err.name === 'TokenExpiredError') {
        throw new UnauthorizedException(CustomErrorMessages.AUTH.REFRESH_TOKEN_EXPIRED);
      } else {
        throw new UnauthorizedException(CustomErrorMessages.AUTH.INVALID_REFRESH_TOKEN);
      }
    }
  }

  async resetPassword(adminResetPasswordDto: AdminResetPasswordDto) {
    let admin = await this.em.findOneBy(Admin, {
      email: Equal(adminResetPasswordDto.email),
    });
    if (!admin) {
      throw new BadRequestException(CustomErrorMessages.USER.USER_NOT_FOUND);
    }

    let secret = await generateSecret();
    let temp_password = await bcrypt.hash(secret, 10);
    admin.temp_password = temp_password;
    await Promise.all([
      this.em.save(Admin, admin),
      this.emailQueue.add(
        'reset',
        {
          temp_password: secret,
          email: admin.email,
          name: admin.name,
        },
        {
          removeOnComplete: true,
        },
      ),
    ]);

    return 'Temporary password have been send to request email';
  }

  async changePassword(admin: Admin, adminChangePasswordDto: AdminChangePasswordDto) {
    let ori_admin = await this.em.findOneBy(Admin, {
      email: Equal(admin.email),
    });
    if (!ori_admin) {
      throw new BadRequestException(CustomErrorMessages.USER.USER_NOT_FOUND);
    }
    let verify = await bcrypt.compare(adminChangePasswordDto.old_password, ori_admin.password);
    if (!verify) {
      throw new BadRequestException(CustomErrorMessages.USER.INVALID_CREDENTIAL);
    } else if (adminChangePasswordDto.new_password != adminChangePasswordDto.confirm_password) {
      throw new BadRequestException(CustomErrorMessages.USER.PASSWORD_MISMATCH);
    } else if (adminChangePasswordDto.new_password == adminChangePasswordDto.old_password) {
      throw new BadRequestException(CustomErrorMessages.USER.SIMILAR_PASSWORD);
    }
    let new_password = await bcrypt.hash(adminChangePasswordDto.new_password, 10);
    ori_admin.password = new_password;
    await this.em.save(Admin, ori_admin);
    return 'You should receive the email within a few moments. Please check your inbox.';
  }

  // Api
  async getAllApiKeys() {
    const apis = await this.em.find(Api, {
      relations: ['platform'],
    })

    // Separate main users and build sub-user mapping
    const subUserMap = new Map<number, number[]>()
    const mainUsers: Api[] = []

    for (const api of apis) {
      if (api.main_user_id) {
        const list = subUserMap.get(api.main_user_id) ?? []
        list.push(api.user_id)
        subUserMap.set(api.main_user_id, list)
      } else {
        mainUsers.push(api)
      }
    }

    // Attach sub_user_ids
    return Promise.all(
      mainUsers.map(async (api) => {
        return {
          ...api,
          secret: undefined,
          main_user_id: undefined,
          sub_user_ids: subUserMap.get(api.user_id) ?? [],
        }
      })
    )
  }

  async toggleStatus(toggleApiDto: ToggleApiDto) {
    const mainApi = await this.em.findOneBy(Api, {
      id: Equal(toggleApiDto.id),
      main_user_id: IsNull(),
    })

    if (!mainApi) {
      throw new BadRequestException(CustomErrorMessages.API.API_NOT_FOUND)
    }

    const subApis = await this.em.findBy(Api, {
      main_user_id: Equal(mainApi.user_id),
    })

    const newStatus = !mainApi.is_active
    mainApi.is_active = newStatus

    // If reactivating the API, recreate listen key for main user
    if (newStatus) {
      const binanceHelper = new BinanceHelper(mainApi.api, await decrypted(mainApi.secret, configuration().encryption.secret))
      mainApi.listen_key = await binanceHelper.getListenKey()
      mainApi.future_key = null // TODO: will get future key in the future when future function is out
    }

    for (const subApi of subApis) {
      subApi.is_active = newStatus
    }

    await this.em.save([mainApi, ...subApis])

    this.datastreamBinanceService.binanceSpotUserStream()

    return 'API status toggled successfully'
  }

  async recreateListenKeys() {
    const activeMainApis = await this.em.findBy(Api, {
      is_active: Equal(true),
      main_user_id: IsNull(),
      platform: { id: Equal(1) },
    })

    for (const api of activeMainApis) {
      try {
        const binanceHelper = new BinanceHelper(api.api, await decrypted(api.secret, configuration().encryption.secret))
        const newListenKey = await binanceHelper.getListenKey()
        api.listen_key = newListenKey
        await this.em.save(api)
      } catch (error) {
        this.pinoLogger.error(`Failed to recreate listen key for API ID: ${api.id}. ${error.message}`)
      }
    }

    this.datastreamBinanceService.binanceSpotUserStream()

    return 'All listen keys have been recreated successfully'
  }

  // Platform
  async createPlatform(createPlatformDto: CreatePlatformDto) {
    const platform = await this.em.findOneBy(Platform, {
      name: Equal(createPlatformDto.name),
    });

    if (platform) {
      throw new BadRequestException(CustomErrorMessages.PLATFORM.PLATFORM_EXIST);
    }

    await this.em.save(Platform, {
      name: createPlatformDto.name
    })

    return `Platform ${createPlatformDto.name} has been created successfully`;
  }

  async updatePlatform(updatePlatformDto: UpdatePlatformDto) {
    const platform = await this.em.createQueryBuilder(Platform, 'p')
      .where('p.id = :id', { id: updatePlatformDto.id })
      .getOne()

    if (!platform)
      throw new BadRequestException(CustomErrorMessages.PLATFORM.NOT_FOUND_PLATFORM)

    // To ensure unique name
    const checkPlatform = await this.em.findOneBy(Platform, {
      name: Equal(updatePlatformDto.name),
    });

    if (checkPlatform) {
      if (checkPlatform.id != platform.id) {
        throw new BadRequestException(CustomErrorMessages.PLATFORM.PLATFORM_EXIST)
      }
    }

    if (updatePlatformDto.name == platform.name && updatePlatformDto.is_active == platform.is_active) {
      return "There is no changes between your data"
    }

    platform.name = updatePlatformDto.name
    platform.is_active = updatePlatformDto.is_active

    this.em.save(platform)

    return `Platform ${updatePlatformDto.id} has been updated successfully`
  }

  // Signal
  async createSignal(createSignalDto: CreateSignalDto) {
    const signal = await this.em.findOneBy(Signal, {
      name: Equal(createSignalDto.name),
      market: Equal(createSignalDto.market),
    });

    if (signal) {
      throw new BadRequestException(CustomErrorMessages.SIGNAL.SIGNAL_EXISTS);
    }

    await this.em.save(Signal, createSignalDto);

    return `Signal ${createSignalDto.name} has been created successfully`;
  }

  async updateSignal(updateSignalDto: UpdateSignalDto) {
    const signal = await this.em.findOneBy(Signal, {
      id: Equal(updateSignalDto.id),
    });

    if (!signal) {
      throw new BadRequestException(CustomErrorMessages.SIGNAL.SIGNAL_NOT_FOUND(updateSignalDto.id));
    }

    const existingSignal = await this.em.findOneBy(Signal, {
      name: Equal(updateSignalDto.name),
      market: Equal(updateSignalDto.market),
    });

    if (existingSignal && existingSignal.id !== signal.id) {
      throw new BadRequestException(CustomErrorMessages.SIGNAL.SIGNAL_EXISTS);
    }

    signal.name = updateSignalDto.name;
    signal.market = updateSignalDto.market;
    signal.description = updateSignalDto.description;
    signal.is_active = updateSignalDto.is_active;

    await this.em.save(signal);

    return `Signal ${updateSignalDto.id} has been updated successfully`;
  }

  // Pair
  async createPair(createPairDto: CreatePairDto) {
    const pairName = createPairDto.base.concat(createPairDto.quote)
    const pair = await this.em.findOneBy(Pair, {
      name: Equal(pairName),
      platform: { id: Equal(createPairDto.platform_id) },
      market: Equal(createPairDto.market),
    });

    if (pair)
      throw new BadRequestException(CustomErrorMessages.PAIR.PAIR_EXISTS)

    const platform = await this.em.findOneBy(Platform, {
      id: Equal(createPairDto.platform_id),
    });

    if (!platform)
      throw new BadRequestException(CustomErrorMessages.PLATFORM.NOT_FOUND_PLATFORM)

    await this.em.save(Pair, {
      name: pairName,
      base: createPairDto.base,
      quote: createPairDto.quote,
      market: createPairDto.market,
      platform,
      price_dp: createPairDto.price_dp,
      volume_dp: createPairDto.volume_dp,
      status: createPairDto.status,
      default_step: createPairDto.default_step,
    })

    return `Pair ${pairName} created successfully`
  }

  async updatePair(updatePairDto: UpdatePairDto) {
    const pair = await this.em.findOneBy(Pair, {
      id: Equal(updatePairDto.id),
    });

    if (!pair)
      throw new BadRequestException("Pair Id: " + CustomErrorMessages.PAIR.PAIR_NOT_FOUND)

    const platform = await this.em.findOneBy(Platform, {
      id: Equal(updatePairDto.platform_id),
    });

    if (!platform)
      throw new BadRequestException(CustomErrorMessages.PLATFORM.NOT_FOUND_PLATFORM)

    const pairName = updatePairDto.base.concat(updatePairDto.quote)

    const existPair = await this.em.findOneBy(Pair, {
      name: Equal(pairName),
      platform: { id: Equal(updatePairDto.platform_id) },
      market: Equal(updatePairDto.market),
    });

    if (existPair && existPair.id != pair.id)
      throw new BadRequestException(CustomErrorMessages.PAIR.PAIR_EXISTS)

    const { id, ...safeDto } = updatePairDto;
    const updatedPair = this.em.merge(Pair, pair, { ...safeDto, platform, name: pairName })

    await this.em.save(updatedPair)

    return `Pair ${updatePairDto.id} updated successfully`
  }

  // async login2fa(req, login2FADto: AdminLogin2FADto) {
  //   if (req.is_2fa !== true) {
  //     throw new UnauthorizedException(
  //       'Please bind 2fa before proceed to 2fa login',
  //     );
  //   }
  //   if (req.email !== login2FADto.email) {
  //     throw new BadRequestException('email is not match with session');
  //   }
  //   let user = await this.em.findOneBy(Admin, {
  //     email: Equal(login2FADto.email),
  //   });
  //   if (!user) {
  //     throw new BadRequestException('Account not exists');
  //   }
  //   verify2FA(
  //     await decrypted(user.g2a, configuration().jwtConstant.jwtAccessSecret),
  //     login2FADto.google_2fa,
  //   );
  //   let accessToken = this.jwtService.sign(
  //     { admin: admin },
  //     {
  //       secret: configuration().jwtConstant.jwtAccessSecret,
  //     },
  //   );

  //   return { email: plainToClass(User, user), accessToken };
  // }

  // async bind2FA(admin: Admin, body: Bind2FADto) {
  //   if (admin.g2a) {
  //     throw new BadRequestException('2FA is binded before');
  //   }
  //   verify2FA(body.secret, body.google_2fa);
  //   admin.g2a = await encrypted(
  //     body.secret,
  //     configuration().jwtConstant.jwtAccessSecret,
  //   );
  //   await this.em.save(Admin, admin);
  //   return 'Bind successful';
  // }

  // async prebind2FA(req, body: Bind2FADto) {
  //   if (req.is_2fa == true) {
  //     throw new UnauthorizedException('This account is binded 2FA before');
  //   }
  //   let admin = await this.em
  //     .createQueryBuilder(Admin, 'admin')
  //     .where('admin.email = :email', { email: req.email })
  //     .getOne();
  //   if (admin.g2a) {
  //     throw new BadRequestException('2FA is binded before');
  //   }
  //   console.log(body);
  //   verify2FA(body.secret, body.google_2fa);
  //   admin.g2a = await encrypted(
  //     body.secret,
  //     configuration().jwtConstant.jwtAccessSecret,
  //   );
  //   await this.em.save(Admin, admin);
  //   let accessToken = this.jwtService.sign(
  //     { admin: admin },
  //     {
  //       secret: configuration().jwtConstant.jwtAccessSecret,
  //     },
  //   );

  //   return { email: plainToClass(User, admin), accessToken };
  //   // return 'Bind successful';
  // }

  // async unbind2FA(admin: Admin, body: Unbind2FADto) {
  //   if (!admin.g2a) {
  //     throw new BadRequestException('2FA is not bind before');
  //   }
  //   let secret = await decrypted(admin.g2a, configuration().jwtConstant.jwtAccessSecret);
  //   verify2FA(secret, body.google_2fa);
  //   admin.g2a = null;
  //   await this.em.save(Admin, admin);
  //   return 'Unbind successful';
  // }
}
