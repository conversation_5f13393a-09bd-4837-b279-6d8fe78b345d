import { BadRequestException, Injectable } from '@nestjs/common';
import { CreateApiDto } from './dto/create-api.dto';
import { UpdateApiDto } from './dto/update-api.dto';
import { EntityManager, Equal, FindOptionsWhere, IsNull, Not } from 'typeorm';
import { Platform } from 'src/platform/entities/platform.entity';
import { CustomErrorMessages } from 'common/helper/errorMessage';
import { Api } from './entities/api.entity';
import { User } from 'src/user/entities/user.entity';
import { decrypted, encrypted } from 'common/util/utils';
import configuration from 'config/configuration';
import { GetApiQueryDto } from './dto/get-api-query.dto';
import { BinanceHelper } from 'common/helper/binance';
import { Cron } from '@nestjs/schedule';
import { DatastreamBinanceService } from 'src/datastream/datastream.binance.service';
import { PinoLogger } from 'nestjs-pino';
import { instanceToPlain } from 'class-transformer';

@Injectable()
export class ApiService {
  constructor(
    private readonly em: EntityManager,
    private readonly datastreamBinanceService: DatastreamBinanceService,
    private readonly pinoLogger: PinoLogger,
  ) {
    this.pinoLogger.setContext(ApiService.name)
  }

  async create(createApiDto: CreateApiDto, user: User) {
    // Block if the user is a sub-user
    let api = await this.em.findOneBy(Api, {
      user_id: Equal(user.id),
    })

    if (api?.main_user_id) {
      throw new BadRequestException(CustomErrorMessages.API.UNPRIVILEGE_CREATION)
    }

    // Validate platform existence
    const platform = await this.em.findOneBy(Platform, {
      id: Equal(createApiDto.platform_id),
    });

    if (!platform) {
      throw new BadRequestException(CustomErrorMessages.PLATFORM.NOT_FOUND_PLATFORM);
    }

    // Prevent duplicate API key for same platform
    api = await this.em.findOneBy(Api, {
      user_id: Equal(user.id),
      platform: { id: Equal(createApiDto.platform_id) },
    })

    if (api) {
      throw new BadRequestException(CustomErrorMessages.API.DUPLICATE_API_KEY);
    }

    const secret = await encrypted(createApiDto.secret, configuration().encryption.secret)

    // Prevent duplicate API or secret key
    api = await this.em.findOne(Api, {
      where: [
        { api: Equal(createApiDto.api) },
        { secret: Equal(secret) },
      ]
    })

    if (api) {
      throw new BadRequestException(CustomErrorMessages.API.DUPLICATE_API_KEY_OR_SECRET);
    }

    // Validate Binance API credentials
    const binanceHelper = new BinanceHelper(createApiDto.api, createApiDto.secret)
    await binanceHelper.getApiKeyPermission()

    const listenKey = await binanceHelper.getListenKey()

    // Save Api record
    await this.em.save(Api, {
      user_id: user.id,
      api: createApiDto.api,
      secret,
      platform,
      listen_key: listenKey,
    })

    // Restart Binance user stream
    this.datastreamBinanceService.binanceSpotUserStream()

    return 'API created successfully';
  }

  async find(getApiQueryDto: GetApiQueryDto, user: User) {
    // Block if the user is a sub-user
    const api = await this.em.findOneBy(Api, {
      user_id: Equal(user.id),
    })

    if (!api) {
      return []
    }
    if (api?.main_user_id) {
      throw new BadRequestException(CustomErrorMessages.API.UNPRIVILEGE_READ)
    }

    const where: FindOptionsWhere<Api> = {
      user_id: Equal(user.id),
    }

    if (getApiQueryDto.platform_id) {
      where.platform = { id: Equal(getApiQueryDto.platform_id) }
    }

    const apis = await this.em.find(Api, {
      where,
      relations: ['platform'],
    })

    return instanceToPlain(apis)
  }

  async update(updateApiDto: UpdateApiDto, user: User) {
    const api = await this.em.findOneBy(Api, {
      id: Equal(updateApiDto.id),
      user_id: Equal(user.id),
    })

    if (!api)
      // Check if the target API exists
      throw new BadRequestException(CustomErrorMessages.API.API_NOT_FOUND)
    if (api.main_user_id)
      // Block if the user is a sub-user
      throw new BadRequestException(CustomErrorMessages.API.UNPRIVILEGE_UPDATE)

    // Encrypt new secret and decrypt existing secret
    const encryptionKey = configuration().encryption.secret
    const [newSecretEncrypted, oldSecretDecrypted] = await Promise.all([
      encrypted(updateApiDto.secret, encryptionKey),
      decrypted(api.secret, encryptionKey),
    ])

    // Prevent duplicate API or secret key
    const existingApiOrSecret = await this.em.findOne(Api, {
      where: [
        { api: Equal(updateApiDto.api), user_id: Not(Equal(user.id)) },
        { secret: Equal(newSecretEncrypted), user_id: Not(Equal(user.id)) },
      ]
    })

    if (existingApiOrSecret) {
      throw new BadRequestException(CustomErrorMessages.API.DUPLICATE_API_KEY_OR_SECRET);
    }

    const binanceHelper = new BinanceHelper(updateApiDto.api, updateApiDto.secret);

    // Update listen key if API credentials changed or account being reactivated
    const shouldUpdateListenKey =
      api.api !== updateApiDto.api ||
      oldSecretDecrypted !== updateApiDto.secret ||
      (!api.is_active && updateApiDto.is_active)

    if (shouldUpdateListenKey) {
      // Verify API permissions if credentials changed
      if (api.api !== updateApiDto.api || oldSecretDecrypted !== updateApiDto.secret) {
        await binanceHelper.getApiKeyPermission()
      }

      api.listen_key = await binanceHelper.getListenKey()
      api.future_key = null // TODO: will get future key in the future when future function is out
    }

    api.api = updateApiDto.api
    api.secret = newSecretEncrypted
    api.is_active = updateApiDto.is_active

    await this.em.save(api)

    // Restart Binance user stream
    this.datastreamBinanceService.binanceSpotUserStream()

    return 'API updated successfully'
  }

  @Cron('28 * * * *', {
    name: 'keepAliveListenKey',
  })
  async keepAliveListenKey() {
    this.pinoLogger.info('Cron job running: keepAliveListenKey');

    const apis = await this.em.findBy(Api, {
      is_active: Equal(true),
      main_user_id: IsNull(),
    })

    for (const api of apis) {
      if (!api.listen_key) continue

      const decryptedSecret = await decrypted(api.secret, configuration().encryption.secret)
      const binanceHelper = new BinanceHelper(api.api, decryptedSecret)

      try {
        await binanceHelper.keepAliveListenKey(api.listen_key)
      } catch (err) {
        this.pinoLogger.warn(`Failed to keep alive for API ${api.id}:`, err.message);
      }
    }
  }
}
