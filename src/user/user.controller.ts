import {
  Body,
  Controller,
  Get,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { UserChangePasswordDto } from './dto/change-password.dto';
import { RegisterUserDto } from './dto/register-user.dto';
import { UserLoginDto } from './dto/user-login.dto';
import { UserService } from './user.service';
import { plainToClass } from 'class-transformer';
import { User } from './entities/user.entity';
import { UserResetPasswordDto } from './dto/reset-password.dto';
import { RefreshTokenDto } from 'common/dto/refresh-token.dto';
import { CreateSubAccDto } from './dto/create-sub-acc.dto';
import { ManageFundsDto } from './dto/manage-funds.dto';
import { Throttle } from '@nestjs/throttler';

@Controller('user')
export class UserController {
  constructor(
    private readonly userService: UserService,
  ) { }

  @Post('register')
  @ApiOperation({
    summary: 'Register user',
  })
  async register(@Body() registerUserDto: RegisterUserDto) {
    let ret = await this.userService.register(registerUserDto);

    return { access_token: ret.accessToken, refresh_token: ret.refreshToken }
  }

  @Post('login')
  @ApiOperation({
    summary: 'User login',
  })
  async login(@Body() userLoginDto: UserLoginDto) {
    const ret = await this.userService.login(userLoginDto);

    return { access_token: ret.accessToken, refresh_token: ret.refreshToken };
  }

  @Get('profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Get user profile',
  })
  async getProfile(@Req() req) {
    return plainToClass(User, req.user);
  }

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Logout user',
  })
  async logout(@Req() req: Request) {
    return await this.userService.logout(req)
  }

  @Post('refresh')
  @ApiOperation({
    summary: 'Refresh user access token'
  })
  async refreshToken(@Body() refreshTokenDto: RefreshTokenDto) {
    return await this.userService.refreshToken(refreshTokenDto)
  }

  @Post('reset-password')
  @Throttle({ default: { ttl: 60000, limit: 1 } })
  @ApiOperation({
    summary: 'Reset user password',
    description: `Triggers a password reset process for a user account.

  **Throttle limit:** This endpoint can be called only once every 1 minute.`
  })
  async resetPassword(@Body() userResetPasswordDto: UserResetPasswordDto) {
    return await this.userService.resetPassword(userResetPasswordDto);
  }

  @Post('change-password')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Change user password',
  })
  async changePassword(@Req() req: any, @Body() userChangePasswordDto: UserChangePasswordDto) {
    return await this.userService.changePassword(req.user, userChangePasswordDto);
  }

  // Sub account
  @Post('create-sub-acc')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Create sub account (Main user only)',
    description: 'Creates a sub account and allocates initial funds at the same time.'
  })
  async createSubAccount(@Req() req: any, @Body() createSubAccDto: CreateSubAccDto) {
    return await this.userService.createSubAccount(req.user, createSubAccDto);
  }

  @Post('manage-funds')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Allocate or deallocate funds to main or sub account (Main user only)',
    description: 'If `sub_user_id` is not provided, the operation applies to the main account.'
  })
  async manageFunds(@Body() manageFundsDto: ManageFundsDto, @Req() req: any) {
    return await this.userService.manageFunds(manageFundsDto, req.user);
  }

  // @Post('login-2fa')
  // @ApiOperation({
  //   summary: 'user login',
  // })
  // @UseGuards(Jwt2FAAuthGuard)
  // async login2fa(
  //   @Req() req,
  //   @Body() body: UserLogin2FADto,
  //   @Res({ passthrough: true }) response: Response,
  // ) {
  //   const ret = await this.userService.login2fa(req.user, body);
  //   response.clearCookie('your-own-cookie');
  //   response.cookie('your-own-cookie', ret.access_token, {
  //     httpOnly: true,
  //     sameSite: 'none',
  //     secure: true,
  //     maxAge: 3600 * 60 * 1000,
  //   });

  //   return { email: ret.email };
  // }

  // @Post('bind-2fa')
  // @ApiOperation({
  //   summary: 'user bind 2fa',
  // })
  // @UseGuards(JwtAuthGuard)
  // async bind2fa(@Body() dto: UserBind2faDto, @Req() req) {
  //   let ret = await this.userService.bind2fa(req.user, dto);
  //   return ret;
  // }

  // @Post('unbind-2fa')
  // @ApiOperation({
  //   summary: 'user unbind 2fa',
  // })
  // @UseGuards(JwtAuthGuard)
  // async unbind2fa(@Body() dto: UserUnbind2faDto, @Req() req) {
  //   let ret = await this.userService.unbind2fa(req.user, dto);
  //   return ret;
  // }

  // @Post('prebind-2fa')
  // @ApiOperation({
  //   summary: 'bind user 2fa',
  // })
  // @UseGuards(Jwt2FAAuthGuard)
  // async prebind2fa(
  //   @Body() dto: UserBind2faDto,
  //   @Req() req: any,
  //   @Res({ passthrough: true }) response: Response,
  // ) {
  //   let ret = await this.userService.prebind2FA(req.user, dto);
  //   response.clearCookie('your-own-cookie');
  //   response.clearCookie('your-own-cookie-2fa');
  //   response.cookie('your-own-cookie', ret.access_token, {
  //     httpOnly: true,
  //     sameSite: 'none',
  //     secure: true,
  //     maxAge: 60 * 60 * 1000,
  //   });
  //   return 'Google 2fa binded';
  // }
}
