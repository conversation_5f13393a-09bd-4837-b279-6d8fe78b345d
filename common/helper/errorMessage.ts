export const CustomErrorMessages = {
  AUTH: {
    INVALID_REFRESH_TOKEN: 'Invalid refresh token',
    REFRESH_TOKEN_EXPIRED: 'Refresh token is expired',
  },
  USER: {
    USER_NOT_FOUND: 'User has not registered',
    USER_REGISTERED: 'User has been register before',
    INVALID_CREDENTIAL: 'Invalid email or password',
    SIMILAR_PASSWORD: 'Your new password is too similar to your old password',
    PASSWORD_MISMATCH: 'Password does not match',
  },
  SUBACC: {
    MAX_SUB_ACC: (n: number) => `Only a maximum of ${n} sub accounts are allowed for each platform`,
    EMAIL_TAKEN: 'Email already taken',
    MAIN_API_NOT_FOUND: 'Main account API key not found, please add an API key first',
    UNPRIVILEGE_ALLOCATION: 'Only main user can allocate funds to sub accounts',
    SUB_ACC_NOT_FOUND: 'Sub account not found',
    UNPRIVILEGE_CREATION: 'Only main user can create sub accounts',
    INVALID_ALLOCATION_QTY: 'Allocation quantity must be greater than 0',
    ALLOCATE_TO_SELF: 'Remove sub_user_id if allocating to yourself',
  },
  PAIR: {
    INVALID_PAIR: 'Invalid or inactive trading pair',
    PAIR_NOT_FOUND: 'Pair not found',
    PAIR_MARKET_NOT_MATCH: 'Pair market type does not match with signal market type',
    PAIR_NOT_ACTIVE: 'Pair is not active',
    PAIR_EXISTS: 'Pair already exists',
  },
  PLATFORM: {
    INVALID_PLATFORM: 'Invalid or inactive platform',
    NOT_FOUND_PLATFORM: 'Platform not found',
    PLATFORM_EXIST: 'Platform had existed',
    PLATFORM_NOT_ACTIVE: 'Platform is not active',
  },
  SIGNAL: {
    INVALID_SIGNAL: 'Invalid or inactive signal',
    SIGNAL_EXISTS: 'Signal name already exists for this market type, please choose a different name and market type combination',
    SIGNAL_NOT_FOUND: (id: number = -1) => id != -1 ? `Signal with ID ${id} not found` : 'Signal not found',
    SIGNAL_NOT_ACTIVE: 'Signal is not active',
  },
  USER_PAIR: {
    INVALID_USER_PAIR: 'Invalid or inactive user pair',
    USER_PAIR_EXIST: 'User pair already exists',
    USER_PAIR_NOT_FOUND: 'User pair not found',
  },
  ORDER: {
    ORDER_NOT_FOUND: 'Order not found.',
    MARKET_PRICE_NOT_FOUND: 'Unable to determine market price from order book.',
  },
  DEPOSIT: {
    INVALID_QTY: 'Quantity must be greater than 0',
  },
  API: {
    INVALID_API: 'Invalid or inactive api',
    DUPLICATE_API_KEY: 'You have already added an API key for this platform.',
    DUPLICATE_API_KEY_OR_SECRET: 'API key or secret key already used by another user',
    API_NOT_FOUND: 'API key not found',
    UNPRIVILEGE_CREATION: 'Only main user can create API key',
    UNPRIVILEGE_READ: 'Only main user can read API key',
    UNPRIVILEGE_UPDATE: 'Only main user can update API key',
  },
  WALLET: {
    INSUFFICIENT_BALANCE: (coin: string) => `Insufficient ${coin} balance`,
    INSUFFICIENT_BINANCE_BALANCE: (coin: string) => `Insufficient ${coin} balance in Binance`,
    INSUFFICIENT_FROZEN_BALANCE: (coin: string) => `Insufficient ${coin} in frozen balance`,
  },
};
