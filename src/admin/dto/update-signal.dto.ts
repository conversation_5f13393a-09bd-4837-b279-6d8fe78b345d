import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsInt, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { MarketType } from 'common/interface/market-type.enum';

export class UpdateSignalDto {
    @ApiProperty({ description: 'Signal ID', example: 1 })
    @IsNotEmpty()
    @IsInt()
    id: number;

    @ApiProperty({ description: 'Signal name', example: 'KK Signal' })
    @IsNotEmpty()
    @IsString()
    name: string;

    @ApiProperty({ description: 'Signal market type', enum: MarketType, example: MarketType.SPOT })
    @IsNotEmpty()
    @IsEnum(MarketType)
    market: MarketType;

    @ApiPropertyOptional({ description: 'Signal description', example: 'This is a signal for kk' })
    @IsOptional()
    @IsString()
    description?: string;

    @ApiProperty({ description: 'Signal active status', example: true })
    @IsNotEmpty()
    @IsBoolean()
    is_active: boolean;
}
