import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsInt, IsNotEmpty, IsNumberString, IsOptional, IsString, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { OrderSide, OrderType, StrategyPrefix } from 'common/interface/order.enum';

export class CreateOrderDto {
    @ApiProperty({ description: 'Pair ID', example: 1 })
    @IsNotEmpty()
    @Type(() => Number)
    @IsInt()
    @Min(1)
    pair_id: number

    @ApiProperty({ description: 'Signal ID', example: 17 })
    @IsNotEmpty()
    @IsInt()
    @Min(1)
    signal_id: number

    @ApiProperty({ description: 'Strategy', enum: StrategyPrefix, example: StrategyPrefix.EZBOT })
    @IsNotEmpty()
    @IsEnum(StrategyPrefix)
    strategy: StrategyPrefix

    @ApiProperty({ description: 'Order price', example: '650.5' })
    @IsNotEmpty()
    @IsNumberString()
    price: string

    @ApiProperty({ description: 'Quantity', example: '0.01' })
    @IsNotEmpty()
    @IsNumberString()
    qty: string

    @ApiProperty({ description: 'Order buy or sell', enum: OrderSide, example: OrderSide.BUY })
    @IsNotEmpty()
    @IsEnum(OrderSide)
    side: OrderSide

    @ApiProperty({ description: 'Order Type', enum: OrderType, example: OrderType.LIMIT_MAKER })
    @IsNotEmpty()
    @IsEnum(OrderType)
    type: OrderType

    @ApiPropertyOptional({ description: 'Client Order ID', example: '1234567890' })
    @IsOptional()
    @IsString()
    client_order_id?: string
}
