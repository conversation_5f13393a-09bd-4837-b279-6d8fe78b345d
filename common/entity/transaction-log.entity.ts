import { DecimalJsTransformer } from "common/util/decimal-js-transformer";
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn } from "typeorm";

export enum TransactionAction {
    BUY = 'BUY',
    SELL = 'SELL',
    ALLOCATE = 'ALLOCATE',
    DEALLOCATE = 'DEALLOCATE',
    FREEZE = 'FREEZE',
    UNFREEZE = 'UNFREEZE',
}

@Entity()
export class TransactionLog {
    @PrimaryGeneratedColumn({ type: 'bigint', unsigned: true })
    id: number;

    @Column({ type: 'bigint', unsigned: true })
    wallet_id: number;

    @Column({
        default: '0',
        type: 'decimal',
        precision: 25,
        scale: 8,
        transformer: DecimalJsTransformer,
    })
    qty: string

    @Column({ type: 'enum', enum: TransactionAction })
    action: TransactionAction;

    @Column({ nullable: true })
    client_order_id: string;

    @Column({ nullable: true })
    reason: string;

    @CreateDateColumn()
    created_at: Date;
}
