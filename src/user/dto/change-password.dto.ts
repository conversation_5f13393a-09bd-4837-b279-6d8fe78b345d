import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class UserChangePasswordDto {
  @ApiProperty({ description: 'User existing password', example: 'abc123' })
  @IsNotEmpty()
  @IsString()
  old_password: string;

  @ApiProperty({ description: 'User new password', example: 'abc123' })
  @IsNotEmpty()
  @IsString()
  new_password: string;

  @ApiProperty({ description: 'User confirm password', example: 'abc123' })
  @IsNotEmpty()
  @IsString()
  confirm_password: string;
}
