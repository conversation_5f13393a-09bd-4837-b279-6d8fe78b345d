import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsInt, IsNotEmpty, IsString } from 'class-validator';

export class UpdateApiDto {
    @ApiProperty({ description: 'api ID' })
    @IsNotEmpty()
    @IsInt()
    id: number;

    @ApiProperty({ description: 'api key' })
    @IsNotEmpty()
    @IsString()
    api: string;

    @ApiProperty({ description: 'api secret'})
    @IsNotEmpty()
    @IsString()
    secret: string;

    @ApiProperty({ description: 'api is active' })
    @IsNotEmpty()
    @IsBoolean()
    is_active: boolean;
}
