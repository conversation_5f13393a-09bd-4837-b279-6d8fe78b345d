import {
  BadRequestException,
  ConflictException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import configuration from 'config/configuration';
import { EntityManager, Equal } from 'typeorm';
import { UserChangePasswordDto } from './dto/change-password.dto';
import { RegisterUserDto } from './dto/register-user.dto';
import { UserLoginDto } from './dto/user-login.dto';
import { User } from './entities/user.entity';
import { CustomErrorMessages } from 'common/helper/errorMessage';
import {
  generateSecret,
} from 'common/util/utils';
import { UserResetPasswordDto } from './dto/reset-password.dto';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { RefreshTokenDto } from 'common/dto/refresh-token.dto';
import { BlacklistedToken } from 'common/entity/blacklisted-token.entity';
import { CreateSubAccDto } from './dto/create-sub-acc.dto';
import Decimal from 'decimal.js';
import { ManageFundsDto } from './dto/manage-funds.dto';
import { WalletService } from 'src/wallet/wallet.service';
import { Api } from 'src/api/entities/api.entity';
import { Platform } from 'src/platform/entities/platform.entity';

@Injectable()
export class UserService {
  constructor(
    private readonly em: EntityManager,
    private readonly jwtService: JwtService,
    private readonly walletService: WalletService,
    @InjectQueue('email')
    private readonly emailQueue: Queue,
  ) { }

  async register(registerUserDto: RegisterUserDto) {
    let user = await this.em.findOneBy(User, {
      email: Equal(registerUserDto.email),
    });

    if (user) {
      throw new BadRequestException(CustomErrorMessages.USER.USER_REGISTERED);
    }

    if (registerUserDto.password != registerUserDto.confPassword)
      throw new BadRequestException(CustomErrorMessages.USER.PASSWORD_MISMATCH);

    const password = await bcrypt.hash(registerUserDto.password, 10);

    user = new User();
    user.password = password;
    user.email = registerUserDto.email;
    user.name = registerUserDto.name;
    await this.em.save(user);

    let accessToken = this.jwtService.sign(
      { user: user },
      { secret: configuration().jwtConstant.jwtAccessSecret, expiresIn: configuration().jwtConstant.jwtAccessExpired }
    )

    let refreshToken = this.jwtService.sign(
      { user: user },
      { secret: configuration().jwtConstant.jwtRefreshSecret, expiresIn: configuration().jwtConstant.jwtRefreshExpired }
    )

    return { accessToken, refreshToken };
  }

  async login(userLoginDto: UserLoginDto) {
    const user = await this.em.findOneBy(User, {
      email: Equal(userLoginDto.email),
    });

    if (!user) {
      throw new ConflictException(CustomErrorMessages.USER.USER_NOT_FOUND);
    }

    var verify;
    if (user.temp_password) {
      verify = await bcrypt.compare(
        userLoginDto.password,
        user.temp_password,
      );
      if (!verify) {
        verify = await bcrypt.compare(userLoginDto.password, user.password);
      } else {
        user.password = user.temp_password;
      }
    } else {
      verify = await bcrypt.compare(userLoginDto.password, user.password);
    }
    if (!verify) {
      throw new BadRequestException(CustomErrorMessages.USER.INVALID_CREDENTIAL);
    }

    user.temp_password = null;
    await this.em.save(User, user);

    let accessToken = this.jwtService.sign(
      { user: user },
      { secret: configuration().jwtConstant.jwtAccessSecret, expiresIn: configuration().jwtConstant.jwtAccessExpired }
    )

    let refreshToken = this.jwtService.sign(
      { user: user },
      { secret: configuration().jwtConstant.jwtRefreshSecret, expiresIn: configuration().jwtConstant.jwtRefreshExpired }
    )

    return { accessToken, refreshToken }
  }

  async logout(req: Request) {
    try {
      let blacklist = new BlacklistedToken()
      blacklist.access_token = await bcrypt.hash(req.headers['authorization']?.replace('Bearer ', ''), 10)
      blacklist.refresh_token = await bcrypt.hash(req.headers['refreshtoken']?.replace('Bearer ', ''), 10)
      await this.em.save(blacklist)
    } catch (error) {
      throw new BadRequestException(error)
    }
    return 'User logout successful'
  }

  async refreshToken(refreshTokenDto: RefreshTokenDto) {
    const refreshToken = refreshTokenDto.refresh_token?.replace(/^Bearer\s+/, '')

    try {
      const payload = await this.jwtService.verify(
        refreshToken,
        { secret: configuration().jwtConstant.jwtRefreshSecret }
      )

      let user = await this.em.findOneBy(User, {
        email: Equal(payload.user.email),
      });

      if (!user) throw new UnauthorizedException(CustomErrorMessages.USER.USER_NOT_FOUND)

      let newAccessToken = this.jwtService.sign(
        { user: payload.user },
        { secret: configuration().jwtConstant.jwtAccessSecret, expiresIn: configuration().jwtConstant.jwtAccessExpired }
      )

      let newRefreshToken = this.jwtService.sign(
        { user: payload.user },
        { secret: configuration().jwtConstant.jwtRefreshSecret, expiresIn: configuration().jwtConstant.jwtRefreshExpired }
      )

      return { access_token: newAccessToken, refresh_token: newRefreshToken }
    } catch (err) {
      if (err.name === 'TokenExpiredError') {
        throw new UnauthorizedException(CustomErrorMessages.AUTH.REFRESH_TOKEN_EXPIRED);
      } else {
        throw new UnauthorizedException(CustomErrorMessages.AUTH.INVALID_REFRESH_TOKEN);
      }
    }
  }

  async resetPassword(userResetPasswordDto: UserResetPasswordDto) {
    let user = await this.em.findOneBy(User, {
      email: Equal(userResetPasswordDto.email),
    });
    if (!user) {
      throw new BadRequestException(CustomErrorMessages.USER.USER_NOT_FOUND);
    }

    let secret = await generateSecret();
    let temp_password = await bcrypt.hash(secret, 10);
    user.temp_password = temp_password;
    await Promise.all([
      this.em.save(User, user),
      this.emailQueue.add(
        'reset',
        {
          temp_password: secret,
          email: user.email,
          name: user.name,
        },
        {
          removeOnComplete: true,
        },
      ),
    ]);

    return 'You should receive the email within a few moments. Please check your inbox.';
  }

  async changePassword(user: User, userChangePasswordDto: UserChangePasswordDto) {
    const ori_user = await this.em.findOneBy(User, {
      email: Equal(user.email),
    });

    if (!ori_user) {
      throw new BadRequestException(CustomErrorMessages.USER.USER_NOT_FOUND);
    }

    const verify = await bcrypt.compare(userChangePasswordDto.old_password, ori_user.password);
    if (!verify) {
      throw new BadRequestException(CustomErrorMessages.USER.INVALID_CREDENTIAL);
    } else if (userChangePasswordDto.new_password != userChangePasswordDto.confirm_password) {
      throw new BadRequestException(CustomErrorMessages.USER.PASSWORD_MISMATCH);
    } else if (userChangePasswordDto.new_password == userChangePasswordDto.old_password) {
      throw new BadRequestException(CustomErrorMessages.USER.SIMILAR_PASSWORD);
    }

    const new_password = await bcrypt.hash(userChangePasswordDto.new_password, 10);
    ori_user.password = new_password;
    await this.em.save(User, ori_user);

    return 'Password changed successfully';
  }

  async createSubAccount(user: User, createSubAccDto: CreateSubAccDto) {
    // Check if the allocation qty is less than 0
    if (Decimal(createSubAccDto.allocation_qty).lte(0)) {
      throw new BadRequestException(CustomErrorMessages.SUBACC.INVALID_ALLOCATION_QTY);
    }

    // Only supports Binance platform for now
    if (createSubAccDto.platform_id !== 1)
      throw new BadRequestException('Sub account creation is only supported on Binance for now')

    const platform = await this.em.findOneBy(Platform, {
      id: Equal(createSubAccDto.platform_id),
    })

    if (!platform) {
      throw new BadRequestException(CustomErrorMessages.PLATFORM.NOT_FOUND_PLATFORM);
    } else if (!platform.is_active) {
      throw new BadRequestException(CustomErrorMessages.PLATFORM.PLATFORM_NOT_ACTIVE)
    }

    const api = await this.em.findOneBy(Api, {
      user_id: Equal(user.id),
      platform: { id: Equal(createSubAccDto.platform_id) }
    })

    if (!api) {
      // Check if the user has api assigned to it
      throw new BadRequestException(CustomErrorMessages.SUBACC.MAIN_API_NOT_FOUND);
    } else if (api.main_user_id) {
      // Block if the user is a sub account
      throw new BadRequestException(CustomErrorMessages.SUBACC.UNPRIVILEGE_CREATION)
    }

    // Check if the user has reached the maximum number of sub accounts
    const subUsersCount = await this.em.countBy(Api, {
      main_user_id: Equal(user.id),
      platform: { id: Equal(createSubAccDto.platform_id) },
    })

    if (subUsersCount >= 8) {
      throw new BadRequestException(CustomErrorMessages.SUBACC.MAX_SUB_ACC(8))
    }

    // Check if the email is already taken by another user
    const existingUser = await this.em.findOneBy(User, {
      email: Equal(createSubAccDto.email),
    })

    if (existingUser) {
      throw new BadRequestException(CustomErrorMessages.SUBACC.EMAIL_TAKEN)
    }

    const password = await bcrypt.hash(createSubAccDto.password, 10)

    await this.em.transaction(async (tx) => {
      const newSubUser = await tx.save(User, {
        password,
        email: createSubAccDto.email,
        name: createSubAccDto.name,
      })

      await tx.save(Api, {
        user_id: newSubUser.id,
        platform,
        api: api.api,
        secret: api.secret,
        main_user_id: user.id,
      })

      await this.walletService.manageFunds(
        createSubAccDto.platform_id,
        createSubAccDto.signal_id,
        createSubAccDto.allocation_coin,
        Decimal(createSubAccDto.allocation_qty),
        api,
        newSubUser.id,
        "Initial allocation",
        tx
      )
    })

    return 'Sub account created successfully'
  }

  async manageFunds(manageFundsDto: ManageFundsDto, user: User) {
    // Block if the user is a sub account
    const api = await this.em.findOne(Api, {
      where: {
        user_id: Equal(user.id),
      },
      relations: ['platform'],
    })

    if (api.main_user_id) {
      throw new BadRequestException(CustomErrorMessages.SUBACC.UNPRIVILEGE_ALLOCATION)
    }

    if (manageFundsDto.sub_user_id === user.id) {
      throw new BadRequestException(CustomErrorMessages.SUBACC.ALLOCATE_TO_SELF)
    }

    if (manageFundsDto.sub_user_id) {
      const subApi = await this.em.findOne(Api, {
        where: {
          user_id: Equal(manageFundsDto.sub_user_id),
          main_user_id: Equal(user.id),
        },
        relations: ['platform'],
      })

      if (!subApi) {
        throw new BadRequestException(CustomErrorMessages.SUBACC.SUB_ACC_NOT_FOUND)
      }
    }

    await this.walletService.manageFunds(
      api.platform.id,
      manageFundsDto.signal_id,
      manageFundsDto.coin,
      Decimal(manageFundsDto.qty),
      api,
      manageFundsDto.sub_user_id,
      manageFundsDto.reason
    )

    return 'Funds allocated successfully'
  }

  // async bind2fa(user: User, dto: UserBind2faDto) {
  //   if (user.g2a) {
  //     throw new BadRequestException(CustomErrorMessages.USER.GOOGLE_BINDED);
  //   }
  //   verify2FA(dto.secret, dto.google_2fa);

  //   user.g2a = await encrypted(dto.secret, configuration().encryption.secret);
  //   await this.em.save(user);
  //   return 'Google 2fa binded';
  // }

  // async unbind2fa(user: User, dto: UserUnbind2faDto) {
  //   if (!user.g2a) {
  //     throw new BadRequestException(CustomErrorMessages.USER.GOOGLE_NOT_BINDED);
  //   }
  //   let secret = await decrypted(user.g2a, configuration().encryption.secret);
  //   verify2FA(secret, dto.google_2fa);

  //   user.g2a = null;
  //   await this.em.save(user);
  //   return 'Google 2fa unbinded';
  // }

  // async login2fa(req, login2FADto: UserLogin2FADto) {
  //   if (req.is_2fa !== true) {
  //     throw new UnauthorizedException(
  //       CustomErrorMessages.USER.GOOGLE_NOT_BINDED,
  //     );
  //   }
  //   if (req.email !== login2FADto.email) {
  //     throw new BadRequestException(CustomErrorMessages.USER.SESSION_NOT_MATCH);
  //   }
  //   let user = await this.em.findOneBy(User, {
  //     email: Equal(login2FADto.email),
  //   });
  //   if (!user) {
  //     throw new BadRequestException(CustomErrorMessages.USER.USER_NOT_FOUND);
  //   }
  //   verify2FA(
  //     await decrypted(user.g2a, configuration().encryption.secret),
  //     login2FADto.google_2fa,
  //   );
  //   let accessToken = this.jwtService.sign(
  //     { user: user },
  //     {
  //       secret: configuration().jwtConstant.jwtAccessSecret,
  //     },
  //   );

  //   return { email: plainToClass(User, user), accessToken };
  // }

  // async prebind2FA(req, body: UserBind2faDto) {
  //   if (req.is_2fa == true) {
  //     throw new UnauthorizedException(CustomErrorMessages.USER.GOOGLE_BINDED);
  //   }
  //   let user = await this.em
  //     .createQueryBuilder(User, 'user')
  //     .where('user.email = :email', { email: req.email })
  //     .getOne();
  //   if (user.g2a) {
  //     throw new BadRequestException(CustomErrorMessages.USER.GOOGLE_BINDED);
  //   }
  //   verify2FA(body.secret, body.google_2fa);
  //   user.g2a = await encrypted(body.secret, configuration().encryption.secret);
  //   await this.em.save(User, user);
  //   let accessToken = this.jwtService.sign(
  //     { user: user },
  //     {
  //       secret: configuration().jwtConstant.jwtAccessSecret,
  //     },
  //   );

  //   return { email: plainToClass(User, user), accessToken };
  //   // return 'Bind successful';
  // }

}
