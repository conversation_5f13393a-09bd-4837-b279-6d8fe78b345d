import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class AdminChangePasswordDto {
  @ApiProperty({ description: 'Admin existing password', example: 'abc123' })
  @IsNotEmpty()
  @IsString()
  old_password: string;

  @ApiProperty({ description: 'Admin new password', example: 'abc123' })
  @IsNotEmpty()
  @IsString()
  new_password: string;

  @ApiProperty({ description: 'Admin confirm password', example: 'abc123' })
  @IsNotEmpty()
  @IsString()
  confirm_password: string;
}
