import { ExecutionContext, HttpException, Injectable } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt-user') {
  constructor() {
    super();
  }
  canActivate(context: ExecutionContext) {
    return super.canActivate(context);
  }

  handleRequest(err, user, info) {
    if (err || !user) {
      throw new HttpException(
        {
          status: 401,
          error: 'Authentication failed',
          message: 'Must be logged in before performing any action',
          details: err?.message || info?.message,
        },
        401,
      );
    }
    return user.user;
  }
}
