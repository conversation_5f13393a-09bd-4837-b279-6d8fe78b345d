import { Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import {
  HttpException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import configuration from 'config/configuration';
import { EntityManager, Equal } from 'typeorm';
import * as jwt from 'jsonwebtoken';
import { admin2FACookieExtractor } from './cookie-extractor';
import { Admin } from 'src/admin/entities/admin.entity';

@Injectable()
export class JwtAdmin2FAStrategy extends PassportStrategy(
  Strategy,
  'your-own-admin-cookie-2fa',
) {
  constructor(private readonly em: EntityManager) {
    super({
      jwtFromRequest: admin2FACookieExtractor(),
      ignoreExpiration: false,
      secretOrKey: configuration().jwtConstant.jwtAccessSecret,
      passReqToCallback: true,
    });
  }

  async validate(payload: any) {
    if (!payload.headers.cookie) {
      throw new UnauthorizedException('please provide valid token');
    }
    if (payload.headers.cookie.search('swap2-ad-2fa=') == -1) {
      throw new UnauthorizedException('invalid token');
    }

    try {
      let token_1 = payload.headers.cookie.split(' ');
      let token = token_1
        .find((item) => item.search('swap2-ad-2fa=') != -1)
        .split('swap2-ad-2fa=')[1];
      if (token[token.length - 1] == ';') {
        token = token.slice(0, -1);
      }
      //verify token
      var decode = jwt.verify(token, configuration().jwtConstant.jwtAccessSecret);
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new UnauthorizedException('token is expired');
      }
      if (error.name === 'user is not valid') {
        throw new UnauthorizedException('user is not valid');
      }
      if (error.name === 'please provide valid token') {
        throw new UnauthorizedException('please provide valid token');
      }
      throw new HttpException(error.message, error.status);
    }
    let admin = await this.em.findOneBy(Admin, {
      email: Equal(decode['email']),
    });
    if (!admin) {
      throw new UnauthorizedException('not an admin account');
    }
    // else if (decode['is_2fa'] !== true) {
    //   throw new UnauthorizedException(
    //     'Please bind 2fa before proceed 2fa login',
    //   );
    // }
    return decode;
  }
}
