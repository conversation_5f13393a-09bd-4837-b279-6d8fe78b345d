import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsEnum, IsNotEmpty, IsOptional, IsString } from "class-validator";
import { MarketType } from "common/interface/market-type.enum";

export class CreateSignalDto {
    @ApiProperty({ description: 'Signal name', example: 'KK Signal' })
    @IsNotEmpty()
    @IsString()
    name: string;

    @ApiProperty({ description: 'Signal market type', enum: MarketType, example: MarketType.SPOT })
    @IsNotEmpty()
    @IsEnum(MarketType)
    market: MarketType;

    @ApiPropertyOptional({ description: 'Signal description', example: 'This is a signal for kk' })
    @IsOptional()
    @IsString()
    description?: string;
}
