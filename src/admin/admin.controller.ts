import {
  Controller,
  Get,
  Post,
  Body,
  UseGuards,
  Req,
} from '@nestjs/common';
import { AdminService } from './admin.service';
import { AdminLoginDto } from './dto/admin-login.dto';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtAdminAuthGuard } from 'src/auth/jwt-admin-auth.guard';
import { AdminResetPasswordDto } from './dto/reset-password.dto';
import { AdminChangePasswordDto } from './dto/change-password.dto';
import { LogEvent } from 'common/entity/activity-log.entity';
import { LogActivity } from 'common/decorator/activity-log.decorator';
import { CreatePlatformDto } from './dto/create-platform.dto';
import { UpdatePlatformDto } from './dto/update-platform.dto';
import { CreateSignalDto } from './dto/create-signal.dto';
import { UpdateSignalDto } from './dto/update-signal.dto';
import { CreatePairDto } from './dto/create-pair.dto';
import { UpdatePairDto } from './dto/update-pair.dto';
import { RefreshTokenDto } from 'common/dto/refresh-token.dto';
import { ToggleApiDto } from './dto/toggle-api.dto';
import { Throttle } from '@nestjs/throttler';

@Controller('admin')
@ApiTags('Admin')
export class AdminController {
  constructor(
    private readonly adminService: AdminService
  ) { }

  @Post('login')
  @LogActivity(LogEvent.LOGIN)
  @ApiOperation({
    summary: 'Admin login',
  })
  async login(
    @Body() adminLoginDto: AdminLoginDto,
  ) {
    const ret = await this.adminService.login(adminLoginDto);

    return { access_token: ret.accessToken, refresh_token: ret.refreshToken };
  }

  @Post('logout')
  @LogActivity(LogEvent.LOGOUT)
  @UseGuards(JwtAdminAuthGuard)
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Logout admin',
  })
  async logout(@Req() req: Request) {
    return await this.adminService.logout(req)
  }

  @Post('refresh')
  @LogActivity(LogEvent.REFRESH)
  @ApiOperation({
    summary: 'Refresh admin access token'
  })
  async refreshToken(@Body() refreshTokenDto: RefreshTokenDto) {
    return await this.adminService.refreshToken(refreshTokenDto)
  }

  @Post('reset-password')
  @Throttle({ default: { ttl: 60000, limit: 1 } })
  @LogActivity(LogEvent.RESET_PASSWORD)
  @ApiOperation({
    summary: 'Reset admin password',
    description: `Triggers a password reset process for an admin account.

  **Throttle limit:** This endpoint can be called only once every 1 minute.`
  })
  async resetPassword(@Body() adminResetPasswordDto: AdminResetPasswordDto) {
    return await this.adminService.resetPassword(adminResetPasswordDto);
  }

  @Post('change-password')
  @LogActivity(LogEvent.CHANGE_PASSWORD)
  @ApiOperation({
    summary: 'Change admin password',
  })
  @UseGuards(JwtAdminAuthGuard)
  @ApiBearerAuth('access-token')
  async changePassword(@Req() req: any, @Body() adminChangePasswordDto: AdminChangePasswordDto) {
    return await this.adminService.changePassword(req.user, adminChangePasswordDto);
  }

  // API
  @Get('api')
  @LogActivity(LogEvent.GET_API_KEYS)
  @UseGuards(JwtAdminAuthGuard)
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Get all main user API keys with linked sub-user IDs',
    description: 'Returns only API keys of main users, each including a list of sub-user IDs that belong to them.',
  })
  async getAllApiKeys() {
    return await this.adminService.getAllApiKeys();
  }

  @Post('api/toggle-status')
  @LogActivity(LogEvent.TOGGLE_API_STATUS)
  @UseGuards(JwtAdminAuthGuard)
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Toggle main API key status',
    description: `Toggle an exising main user's API key status by its ID.
  All linked sub-user APIs will be updated to match the same status.`
  })
  async toggleApiStatus(@Body() toggleApiDto: ToggleApiDto) {
    return await this.adminService.toggleStatus(toggleApiDto);
  }

  @Throttle({ default: { ttl: 300000, limit: 1 } })
  @Post('api/recreate-listen-keys')
  @LogActivity(LogEvent.RECREATE_LISTEN_KEYS)
  @UseGuards(JwtAdminAuthGuard)
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: "Recreate listen keys for all active main user API keys",
    description: `Regenerates and updates listen keys for all active API keys belonging to main users.
    
  **Throttle limit:** This endpoint can be called only once every **5** minutes.`
  })
  async recreateListenKeys() {
    return await this.adminService.recreateListenKeys();
  }

  // Platform
  @Post('platform/create')
  @LogActivity(LogEvent.CREATE_PLATFORM)
  @UseGuards(JwtAdminAuthGuard)
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Create a new platform'
  })
  async createPlatform(@Body() createPlatformDto: CreatePlatformDto) {
    return await this.adminService.createPlatform(createPlatformDto)
  }

  @Post('platform/update')
  @LogActivity(LogEvent.UPDATE_PLATFORM)
  @UseGuards(JwtAdminAuthGuard)
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Update an existing platform',
    description: 'Updates the details of an existing platform by its ID.'
  })
  async updatePlatform(@Body() updatePlatformDto: UpdatePlatformDto) {
    return await this.adminService.updatePlatform(updatePlatformDto);
  }

  // Signal
  @Post('signal/create')
  @LogActivity(LogEvent.CREATE_SIGNAL)
  @ApiOperation({
    summary: 'Create a new trading signal',
    description: `Creates a new trading signal.
    
  - \`market\` (enum): Market type. Supported values: \`SPOT\`, \`FUTURE\`.`
  })
  @UseGuards(JwtAdminAuthGuard)
  @ApiBearerAuth('access-token')
  async createSignal(@Body() createSignalDto: CreateSignalDto) {
    return await this.adminService.createSignal(createSignalDto);
  }

  @Post('signal/update')
  @LogActivity(LogEvent.UPDATE_SIGNAL)
  @ApiOperation({
    summary: 'Update an existing signal',
    description: `Updates the details of an existing signal by its ID.

  - \`market\` (enum): Market type. Supported values: \`SPOT\`, \`FUTURE\`.`
  })
  @UseGuards(JwtAdminAuthGuard)
  @ApiBearerAuth('access-token')
  async updateSignal(@Body() updateSignalDto: UpdateSignalDto) {
    return await this.adminService.updateSignal(updateSignalDto);
  }

  // Pair
  @Post('pair/create')
  @LogActivity(LogEvent.CREATE_PAIR)
  @ApiOperation({
    summary: 'Create a new pair',
    description: `Creates a new trading pair for a given platform.
  
  - \`market\` (enum): The market type. Supported values: \`SPOT\`, \`FUTURE\`.
  - \`status\` (enum): The status of the pair. Supported values: \`ACTIVE\`, \`INACTIVE\`, \`DEPRECATED\`.`
  })
  @UseGuards(JwtAdminAuthGuard)
  @ApiBearerAuth('access-token')
  async createPair(@Body() createPairDto: CreatePairDto) {
    return await this.adminService.createPair(createPairDto);
  }

  @Post('pair/update')
  @LogActivity(LogEvent.UPDATE_PAIR)
  @ApiOperation({
    summary: 'Update an existing trading pair',
    description: `Updates the details of an existing trading pair by its ID.

  - \`market\` (enum): Market type. Supported values: \`SPOT\`, \`FUTURE\`.
  - \`status\` (enum): Pair status. Supported values: \`ACTIVE\`, \`INACTIVE\`, \`DEPRECATED\`.`
  })
  @UseGuards(JwtAdminAuthGuard)
  @ApiBearerAuth('access-token')
  async updatePair(@Body() updatePairDto: UpdatePairDto) {
    return await this.adminService.updatePair(updatePairDto);
  }

  // @Post('login-2fa')
  // @ApiOperation({
  //   summary: 'admin login',
  // })
  // @UseGuards(JwtAdmin2FAAuthGuard)
  // async login2fa(
  //   @Req() req,
  //   @Body() body: AdminLogin2FADto,
  //   @Res({ passthrough: true }) response: Response,
  // ) {
  //   const ret = await this.adminService.login2fa(req.user, body);
  //   response.clearCookie('swap2-ad');
  //   response.cookie('swap2-ad', ret.access_token, {
  //     httpOnly: true,
  //     sameSite: 'none',
  //     secure: true,
  //     maxAge: 15 * 60 * 1000,
  //   });

  //   return;
  // }

  // @Post('bind-2fa')
  // @ApiOperation({
  //   summary: 'bind admin 2fa',
  // })
  // @UseGuards(JwtAdminAuthGuard)
  // async bind2fa(@Body() dto: Bind2FADto, @Req() req: any) {
  //   return await this.adminService.bind2FA(req.user, dto);
  // }

  // @Post('prebind-2fa')
  // @ApiOperation({
  //   summary: 'bind admin 2fa',
  // })
  // @UseGuards(JwtAdmin2FAAuthGuard)
  // async prebind2fa(
  //   @Body() dto: Bind2FADto,
  //   @Req() req: any,
  //   @Res({ passthrough: true }) response: Response,
  // ) {
  //   let ret = await this.adminService.prebind2FA(req.user, dto);
  //   response.clearCookie('swap2-ad');
  //   response.clearCookie('swap2-ad-2fa');
  //   response.cookie('swap2-ad', ret.access_token, {
  //     httpOnly: true,
  //     sameSite: 'none',
  //     secure: true,
  //     maxAge: 15 * 60 * 1000,
  //   });
  //   return;
  // }

  // @Post('unbind-2fa')
  // @ApiOperation({
  //   summary: 'unbind admin 2fa',
  // })
  // @UseGuards(JwtAdminAuthGuard)
  // async unbind2fa(@Body() dto: Unbind2FADto, @Req() req: any) {
  //   return await this.adminService.unbind2FA(req.user, dto);
  // }
}
