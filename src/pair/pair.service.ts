import { Injectable } from '@nestjs/common';
import { GetPairQueryDto } from './dto/get-pair-query.dto';
import { EntityManager } from 'typeorm';
import { Pair } from './entities/pair.entity';

@Injectable()
export class PairService {
  constructor(
    private readonly em: EntityManager
  ) { }

  async find(getPairQueryDto: GetPairQueryDto) {
    const query = this.em.createQueryBuilder(Pair, 'p')
      .leftJoinAndSelect('p.platform', 'platform')
      .select()

    if (getPairQueryDto.platform_id)
      query.andWhere('p.platform_id = :platform_id', { platform_id: getPairQueryDto.platform_id })

    if (getPairQueryDto.name)
      query.andWhere('p.name LIKE :name', { name: `%${getPairQueryDto.name}%` })

    if (getPairQueryDto.market)
      query.andWhere('p.market = :market', { market: getPairQueryDto.market })

    if (getPairQueryDto.status)
      query.andWhere('p.status = :status', { status: getPairQueryDto.status })

    const pairs = await query.getMany()

    return pairs
  }
}
