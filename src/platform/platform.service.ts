import { Injectable } from '@nestjs/common';
import { Platform } from './entities/platform.entity';
import { EntityManager } from 'typeorm';
import { GetPlatformQueryDto } from './dto/get-platform-query.dto';

@Injectable()
export class PlatformService {
  constructor(
    private readonly em: EntityManager,
  ) { }

  async find(getPlatformQueryDto: GetPlatformQueryDto) {
    const platforms = this.em.createQueryBuilder(Platform, 'p')
    
    if (getPlatformQueryDto.is_active !== undefined) {
      platforms.andWhere('p.is_active = :is_active', { is_active: getPlatformQueryDto.is_active })
    }

    return await platforms.getMany()
  }
}
