import { MarketType } from "common/interface/market-type.enum";
import { Signal } from "src/signal/entities/signal.entity";
import { DataSource } from "typeorm";
import { <PERSON>noLogger } from "nestjs-pino";

export async function seederSignal(dataSource: DataSource) {
    const pinoLogger = new PinoLogger({ pinoHttp: {} })
    const repo = dataSource.getRepository(Signal)
    const count = await repo.count()

    pinoLogger.setContext(seederSignal.name)

    if (count === 0) {
        await repo.save([
            {
                id: 1,
                name: 'KK Signal',
                market: MarketType.SPOT,
                description: 'Danish Buy Hardcode Sell',
            },
            {
                id: 2,
                name: 'Dynamic Profit',
                market: MarketType.SPOT,
                description: 'Danish Buy ATR Sell',
            },
            {
                id: 3,
                name: 'Shan Wen Strategy',
                market: MarketType.SPOT,
                description: 'Simple No Cut Lost DCA',
            },
            {
                id: 5,
                name: 'jj future',
                market: MarketType.FUTURE,
                description: 'Danish Buy and Sell',
            },
            {
                id: 11,
                name: 'Chia Strategy',
                market: MarketType.SPOT,
            },
            {
                id: 12,
                name: 'Sia Strategy',
                market: MarketType.SPOT,
            },
            {
                id: 13,
                name: 'Aina Strategy',
                market: MarketType.SPOT,
            },
            {
                id: 14,
                name: 'Jun Hui Strategy',
                market: MarketType.SPOT,
            },
            {
                id: 15,
                name: 'Song Strategy',
                market: MarketType.SPOT,
            },
            {
                id: 16,
                name: 'KK SR',
                market: MarketType.SPOT,
                description: 'Danish Long/Exit/CL',
            },
            {
                id: 17,
                name: 'EZBOT',
                market: MarketType.SPOT,
            },
            {
                id: 18,
                name: 'HODL',
                market: MarketType.SPOT,
            },
        ])
        pinoLogger.info('[SEED] Signal table seeded.')
    }
}