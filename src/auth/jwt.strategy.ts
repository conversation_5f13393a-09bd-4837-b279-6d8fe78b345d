import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy, ExtractJwt } from 'passport-jwt';
import configuration from 'config/configuration';
import { EntityManager, Equal } from 'typeorm';
import { User } from '../user/entities/user.entity';

@Injectable()
export class JwtStrategy extends PassportStrategy(
  Strategy,
  'jwt-user',
) {
  constructor(private readonly em: EntityManager) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configuration().jwtConstant.jwtAccessSecret,
    });
  }

  async validate(payload: any) {
    try {
      const user = await this.em.findOneBy(User, {
        email: Equal(payload.user.email),
      });

      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      return { user: user };
    } catch (error) {
      throw new UnauthorizedException(error.message); 
    }
  } 
}
