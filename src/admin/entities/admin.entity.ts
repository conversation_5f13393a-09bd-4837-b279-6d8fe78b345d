import { ActivityLog } from 'common/entity/activity-log.entity';
import {
  Column,
  CreateDateColumn,
  Entity,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Exclude } from 'class-transformer';

@Entity()
export class Admin {
  @PrimaryGeneratedColumn({ unsigned: true })
  id: number;

  @Column()
  name: string;

  @Column({ unique: true })
  email: string;
  
  @Column({ nullable: true })
  @Exclude()
  email_verified_at: Date;
  
  @Column()
  @Exclude()
  password: string;

  @Column({ default: null, nullable: true })
  @Exclude()
  temp_password: string;

  @Column({ nullable: true })
  @Exclude()
  g2a: string;

  @OneToMany((type) => ActivityLog, (activity_log) => activity_log.admin)
  activity_log: ActivityLog[];

  @CreateDateColumn()
  created_at: string;

  @UpdateDateColumn()
  updated_at: string;
}
