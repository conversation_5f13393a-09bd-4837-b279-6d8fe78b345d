import { Spot, SPOT_REST_API_PROD_URL } from '@binance/spot'
import { Wallet, WALLET_REST_API_PROD_URL } from '@binance/wallet'
import { SimpleEarn, SIMPLE_EARN_REST_API_PROD_URL } from '@binance/simple-earn'
import { BadRequestException } from '@nestjs/common'
import { OrderSide, OrderType, StrategyPrefix } from 'common/interface/order.enum'
import { getRedis, incrementWithExpiry, insertRedis } from './redis'

export class BinanceHelper {
    private spotClient: Spot
    private walletClient: Wallet
    private simpleEarnClient: SimpleEarn
    private readonly IP_RATE_LIMIT_KEY = 'rate_limit:ip'
    private readonly IP_MAX_RATE = 6000

    // api and secret keys are optional if using functions that are commented with 'Public API'
    constructor(apiKey?: string, apiSecret?: string) {
        const hasKeys = !!apiKey && !!apiSecret

        const getConfig = (basePath: string) => ({
            configurationRestAPI: {
                ...(hasKeys && { apiKey, apiSecret }),
                basePath,
            }
        })

        this.spotClient = new Spot(getConfig(SPOT_REST_API_PROD_URL))
        this.walletClient = new Wallet(getConfig(WALLET_REST_API_PROD_URL))
        this.simpleEarnClient = new SimpleEarn(getConfig(SIMPLE_EARN_REST_API_PROD_URL))
    }

    private async rateLimited<T>(
        weight: number,
        fn: () => Promise<{ data(): T; headers: any }>
    ): Promise<T> {
        const key = this.IP_RATE_LIMIT_KEY;
        const ttl = 60 - new Date().getSeconds(); // Time until the next full minute

        // --- Step 1: Try to increment the local Redis counter ---
        // This acts as a soft rate limiter to prevent unnecessary requests to Binance
        const current = await incrementWithExpiry(key, weight, ttl);

        // --- Step 2: If we’ve already exceeded our allowed limit, block the request ---
        if (current > this.IP_MAX_RATE) {
            throw new BadRequestException(`Too many requests: ${current} > ${this.IP_MAX_RATE}`);
        }

        // --- Step 3: Make the actual API request to Binance ---
        const response = await fn();

        // --- Step 4: Sync Redis with Binance’s actual used weight (if higher) ---
        // Binance provides the real usage count in the response headers.
        // This ensures our local counter stays accurate, especially in race conditions.
        const usedWeightHeader = response.headers['x-mbx-used-weight-1m'];
        if (usedWeightHeader) {
            const actualUsedWeight = parseInt(usedWeightHeader, 10);
            if (!isNaN(actualUsedWeight)) {
                const currentCountStr = await getRedis(key);
                const currentCount = parseInt(currentCountStr) || 0;

                // Only update Redis if Binance’s number is higher (e.g. due to parallel requests)
                if (actualUsedWeight > currentCount) {
                    await insertRedis(key, actualUsedWeight.toString(), ttl);
                }
            }
        }

        // --- Step 5: Return the actual result from Binance ---
        return response.data();
    }

    async newOrder(
        symbol: string,
        side: OrderSide,
        type: OrderType,
        qty: number,
        price: number,
        strategy: StrategyPrefix,
        userId: number,
        signalId: number
    ): Promise<any> {
        const weight = 1

        const orderParams: any = {
            symbol: symbol,
            side: side,
            type: type,
            quantity: qty,
            newOrderRespType: 'FULL',
            newClientOrderId: `${strategy}-${signalId}-${userId}-${Date.now()}`,
            recvWindow: 5000,
        }

        if (type === OrderType.LIMIT_MAKER)
            orderParams.price = price

        try {
            return await this.rateLimited(weight, () =>
                this.spotClient.restAPI.newOrder(orderParams)
            )
        } catch (error) {
            throw new BadRequestException(error.message)
        }
    }

    async cancelOrder(pairName: string, orderId: number): Promise<any> {
        const weight = 1

        try {
            await this.rateLimited(weight, () =>
                this.spotClient.restAPI.deleteOrder({
                    symbol: pairName,
                    orderId,
                })
            )
        } catch (error) {
            throw new BadRequestException(error.message)
        }
    }

    // Public API
    async getOrderBook(pairName: string, limit: number = 10): Promise<any> {
        const weight = limit <= 100 ? 5 : limit <= 500 ? 25 : limit <= 1000 ? 50 : 250

        try {
            return await this.rateLimited(weight, () =>
                this.spotClient.restAPI.depth({
                    symbol: pairName,
                    limit
                })
            )
        } catch (error) {
            throw new BadRequestException(error.message)
        }
    }

    async getSpotBalance(asset?: string): Promise<any> {
        const weight = 5

        try {
            return await this.rateLimited(weight, () =>
                this.walletClient.restAPI.userAsset({ asset: asset?.toUpperCase() || null })
            )
        } catch (error) {
            throw new BadRequestException(error.message)
        }
    }

    async getFlexibleEarnBalance(asset?: string): Promise<any> {
        const weight = 150

        try {
            return await this.rateLimited(weight, () =>
                this.simpleEarnClient.restAPI.getFlexibleProductPosition({ asset: asset?.toUpperCase() || null })
            )
        } catch (error) {
            throw new BadRequestException(error.message)
        }
    }

    // getLockedEarnBalance
    
    async getAssets(asset?: string): Promise<any> {
        try {
            const spot = await this.getSpotBalance(asset)
            const earn = await this.getFlexibleEarnBalance(asset)
            const assets = {}
            spot.forEach(element => {
                assets[element.asset] = { free: element.free, locked: element.locked, freeze: element.freeze, earn: 0 }
            });
            earn.rows.forEach(element => {
                if (assets[element.asset])
                    assets[element.asset]['earn'] = element.totalAmount
                else
                    assets[element.asset] = { free: 0, locked: 0, freeze: 0, earn: element.totalAmount }
            });
            return assets
        } catch (error) {
            throw new BadRequestException(error.message)
        }
    }

    async getApiKeyPermission(): Promise<any> {
        const weight = 1;

        try {
            const data = await this.rateLimited(weight, () =>
                this.walletClient.restAPI.getApiKeyPermission()
            )

            if (!data.ipRestrict)
                throw new BadRequestException('Please whitelist **************')

            if (!data.enableSpotAndMarginTrading)
                throw new BadRequestException('Please enable your Spot And Margin Trading')
        } catch (err) {
            const msg = err?.message || ''

            if (msg.includes("Invalid Api-Key ID"))
                throw new BadRequestException('Please check your api key')
            else if (msg.includes("Invalid API-key, IP, or permissions for action"))
                throw new BadRequestException('Please whitelist ************** or check your secret key')
            else if (msg.includes("Signature for this request is not valid"))
                throw new BadRequestException('Please check your secret key')
            else
                throw new BadRequestException(`getApiKeyPermission: ${msg}`)
        }
    }

    async getListenKey() {
        const weight = 2

        try {
            const data = await this.rateLimited(weight, () =>
                this.spotClient.restAPI.newUserDataStream()
            )

            return data.listenKey
        } catch (err) {
            throw new BadRequestException(err)
        }
    }

    async keepAliveListenKey(listenKey: string) {
        const weight = 2

        try {
            await this.rateLimited(weight, () =>
                this.spotClient.restAPI.putUserDataStream({ listenKey })
            )
        } catch (err) {
            throw new BadRequestException(err)
        }
    }

    async stakeFlexi(asset: string, amount: number): Promise<any> {
        const weight = 1

        try {
            await this.rateLimited(weight, () =>
                this.simpleEarnClient.restAPI.subscribeFlexibleProduct({
                    productId: asset + "001",
                    amount: amount,
                })
            )
        } catch (err) {
            throw new BadRequestException(err)
        }
    }

    async redeemFlexi(asset: string, amount: number): Promise<any> {
        const weight = 1

        try {
            await this.simpleEarnClient.restAPI.redeemFlexibleProduct({
                productId: asset + "001",
                amount: amount,
            })
        } catch (err) {
            throw new BadRequestException(err)
        }
    }

}
