import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { Entity<PERSON>anager, Equal } from 'typeorm';
import { Matching, MatchingStatus } from './entities/matching.entity';
import { Order, OrderStatus } from 'src/order/entities/order.entity';
import { Profit } from 'src/profit/entities/profit.entity';
import { Decimal } from 'decimal.js';
import { Summary } from 'src/summary/entities/summary.entity';

@Injectable()
export class MatchingService {
  constructor(
    private readonly em: EntityManager,
  ) { }

  @Cron(CronExpression.EVERY_5_SECONDS, {
    waitForCompletion: true,
    name: 'matching',
  })
  async autoMatch() {
    const matchings = await this.em.findBy(Matching, {
      status: Equal(MatchingStatus.MATCHING),
    })

    if (!matchings.length) return

    for (const m of matchings) {
      const matchOrder = await this.em.findOneBy(Order, {
        client_order_id: Equal(m.match_id),
      }) // Close Order

      if (!matchOrder) continue // Continue to next iteration

      const order = await this.em.createQueryBuilder(Order, 'o')
        .leftJoinAndSelect('o.user', 'user')
        .where('o.client_order_id = :client_order_id', { client_order_id: m.order_id })
        .getOne() // Open Order

      if (!order) continue // Continue to next iteration

      if (Decimal(matchOrder.filled).equals(Decimal(matchOrder.qty))) {
        if (m.status == MatchingStatus.MATCHING) {
          let buy: Order, sell: Order

          if (order.is_buy == true) {
            buy = order
            sell = matchOrder
          } else {
            buy = matchOrder
            sell = order
          }

          let buyQty = Decimal(buy.filled).sub(Decimal(buy.matched))
          let sellQty = Decimal(sell.filled).sub(Decimal(sell.matched))
          let qty = Decimal(buyQty).greaterThan(Decimal(sellQty)) ? Decimal(sellQty) : Decimal(buyQty)

          let buyPrice = Decimal(buy.price)
          let sellPrice = Decimal(sell.price)

          const profit = await this.em.createQueryBuilder(Profit, 'p')
            .where('p.buy_id = :buy_id', { buy_id: buy.client_order_id })
            .andWhere('p.sell_id = :sell_id', { sell_id: sell.client_order_id })
            .getOne()

          let prft = Decimal(sellPrice).sub(Decimal(buyPrice)).mul(Decimal(qty))
          let profit_pct = (prft.div(Decimal(buy.amount).mul(Decimal(sell.qty)).div(Decimal(buy.qty)))).mul(100)

          if (!profit) {
            let profitDet = new Profit()
            profitDet.buy_id = buy.client_order_id
            profitDet.sell_id = sell.client_order_id
            profitDet.qty = qty.toString()
            profitDet.profit = prft.toString()
            profitDet.profit_pct = profit_pct.toString()
            profitDet.is_long = buy.is_buy

            await this.em.save(profitDet)

            const signalIdStr = order.client_order_id.split('-')[1]

            let summary = await this.em.createQueryBuilder(Summary, 's')
              .leftJoinAndSelect('s.user', 'user')
              .where('s.signal_id = :signalIdStr', { signalIdStr })
              .andWhere('s.pair_name = :pair_name', { pair_name: order.pair_name })
              .andWhere('user.id = :user_id', { user_id: order.user.id })
              .andWhere('DATE(s.created_at) = DATE(:created_at)', { created_at: profitDet.created_at })
              .getOne()

            if (!summary) {
              summary = new Summary()
              summary.pair_name = order.pair_name
              summary.signal_id = parseInt(signalIdStr)
              summary.user = order.user
              summary.realized = '0'
            }

            summary.realized = Decimal(summary.realized).add(Decimal(profitDet.profit)).toString()
            await this.em.save(summary)
          }

          buy.matched = Decimal(buy.matched).add(Decimal(qty)).toString()
          sell.matched = Decimal(sell.matched).add(Decimal(qty)).toString()

          buy.status = Decimal(buy.matched).lessThan(Decimal(buy.filled)) ? OrderStatus.OPEN : OrderStatus.CLOSED
          sell.status = Decimal(sell.matched).lessThan(Decimal(sell.filled)) ? OrderStatus.OPEN : OrderStatus.CLOSED

          await this.em.save(buy)
          await this.em.save(sell)
          m.status = MatchingStatus.MATCHED
          await this.em.save(m)
        }
      }
    }
  }
}
