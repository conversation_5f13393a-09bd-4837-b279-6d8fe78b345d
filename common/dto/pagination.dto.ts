import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsInt, IsOptional, Max, Min } from 'class-validator';

export enum Order {
  ASC = 'ASC',
  DESC = 'DESC',
}

export class PageOptionsDto {
  @ApiPropertyOptional({ enum: Order, default: Order.DESC })
  @IsEnum(Order)
  @IsOptional()
  readonly order?: Order = Order.DESC;

  @ApiPropertyOptional({
    minimum: 1,
    default: 1,
  })
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @IsOptional()
  readonly page?: number = 1;

  @ApiPropertyOptional({
    minimum: 1,
    maximum: 50,
    default: 10,
  })
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(50)
  @IsOptional()
  readonly take?: number = 10;

  get skip(): number {
    return (this.page - 1) * this.take;
  }
}
export function PaginateByObject(
  dataObject: any[],
  pageNumber: number | string,
  pageSize: number | string,
) {
  const page = Math.max(1, Number(pageNumber));
  const size = Math.max(1, Number(pageSize));

  const startIndex = (page - 1) * size;
  const endIndex = startIndex + size;

  return dataObject.slice(startIndex, endIndex);
}

export function dynamicSort(result, field, order = 'ASC') {
  return result.sort((a, b) => {
    let valA = a[field];
    let valB = b[field];

    // Check if the values are strings containing only numbers
    const isNumericA = /^\d+$/.test(valA);
    const isNumericB = /^\d+$/.test(valB);

    // Convert to number if the value is numeric, otherwise keep as string
    valA = isNumericA ? Number(valA) : valA;
    valB = isNumericB ? Number(valB) : valB;

    if (order === 'ASC') {
      return valA > valB ? 1 : valA < valB ? -1 : 0;
    } else {
      return valA < valB ? 1 : valA > valB ? -1 : 0;
    }
  });
}
