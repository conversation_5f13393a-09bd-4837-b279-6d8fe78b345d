import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsInt, IsOptional, IsString, Min } from 'class-validator';
import { OrderStatus } from '../entities/order.entity';
import { Type } from 'class-transformer';

export class GetOrderQueryDto {
    @ApiPropertyOptional({ description: 'Pair Name', example: 'BNBFDUSD' })
    @IsOptional()
    @IsString()
    pair_name: string

    @ApiPropertyOptional({ description: 'Platform ID', example: '1' })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    @Min(1)
    platform_id: number

    @ApiPropertyOptional({ description: 'Signal ID', example: '17' })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    @Min(1)
    signal_id: number

    @ApiPropertyOptional({ description: 'Order status', enum: OrderStatus, example: OrderStatus.OPEN })
    @IsOptional()
    @IsEnum(OrderStatus)
    status: OrderStatus
}