export function user2FACookieExtractor() {
  return function (request) {
    var token = null;
    if (request && request.cookies) {
      token = request.cookies['your-own-cookies-2fa'];
      const ip =
        request.headers['x-forwarded-for'] ||
        request.ip ||
        request.connection.remoteAddress;
      // console.log(`Authentication attempt from IP: ${ip}`);
    }
    return token;
  };
}

export function admin2FACookieExtractor() {
  return function (request) {
    var token = null;
    if (request && request.cookies) {
      token = request.cookies['your-own-admin-cookie-2fa'];
      const ip =
        request.headers['x-forwarded-for'] ||
        request.ip ||
        request.connection.remoteAddress;
      // console.log(`Authentication attempt from IP: ${ip}`);
    }
    return token;
  };
}
