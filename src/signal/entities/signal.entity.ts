import { MarketType } from "common/interface/market-type.enum";
import { UserPair } from "src/user_pair/entities/user_pair.entity";
import { Order } from "src/order/entities/order.entity";
import { Column, CreateDateColumn, Entity, OneToMany, PrimaryGeneratedColumn, Unique, UpdateDateColumn } from "typeorm";

@Entity()
@Unique(["name", "market"])
export class Signal {
    @PrimaryGeneratedColumn({ unsigned: true })
    id: number;

    @Column({ default: '' })
    name: string;

    @Column({ type: "enum", enum: MarketType, default: MarketType.SPOT })
    market: MarketType;

    @Column('longtext', { nullable: true })
    description: string;

    @Column({ default: 1 })
    is_active: boolean;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @OneToMany(() => UserPair, userPair => userPair.signal)
    userPairs: UserPair[];

    @OneToMany(() => Order, (order) => order.signal)
    orders: Order[]
}
