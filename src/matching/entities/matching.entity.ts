import { En<PERSON>ty, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';

export enum MatchingStatus {
  MATCHING = 'MATCHING',
  MATCHED = 'MATCHED',
  CANCELLED = 'CANCELLED'
}

@Entity()
export class Matching {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: number;

  @Column()
  order_id: string;

  @Column({ nullable: true })
  match_id: string;

  @Column({ type: 'enum', enum: MatchingStatus })
  status: MatchingStatus;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
