import { Modu<PERSON> } from '@nestjs/common';
import { AdminService } from './admin.service';
import { AdminController } from './admin.controller';
import { JwtModule } from '@nestjs/jwt';
import configuration from 'config/configuration';
import { BullModule } from '@nestjs/bull';
import { DatastreamModule } from 'src/datastream/datastream.module';

@Module({
  imports: [
    JwtModule.register({
      secret: configuration().jwtConstant.jwtAccessSecret,
      signOptions: { expiresIn: configuration().jwtConstant.jwtAccessExpired },
    }),
    BullModule.registerQueue({
      name: 'email',
      redis: {
        host: configuration().redis.host, // might need change
        port: configuration().redis.port,
        password: configuration().redis.password,
      },
    }),
    DatastreamModule,
  ],
  controllers: [AdminController],
  providers: [AdminService],
})
export class AdminModule {}
