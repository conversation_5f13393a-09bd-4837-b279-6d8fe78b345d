import Decimal from "decimal.js";
import { ValueTransformer } from "typeorm";

export const DecimalJsTransformer: ValueTransformer = {
    // Convert Decimal object to string before saving to DB
    to: (value: Decimal | string | number | null): string => {
        if (value === null || value === undefined) return '0';
        if (value instanceof Decimal) return value.toString();
        return value.toString();
    },

    // Just return the string from DB
    from: (value: string | null) => {
        if (value === null || value === undefined) return 0;
        return value;
    },
};
