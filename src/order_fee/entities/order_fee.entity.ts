import { DecimalJsTransformer } from "common/util/decimal-js-transformer";
import { Order } from "src/order/entities/order.entity";
import { Column, CreateDateColumn, Entity, UpdateDateColumn, PrimaryGeneratedColumn, ManyToOne, JoinColumn } from "typeorm";

export enum FeeSource {
    TRADE = 'TRADE',
    WITHDRAWAL = 'WITHDRAWAL',
    FUNDING = 'FUNDING',
    OTHER = 'OTHER'
}

@Entity()
export class OrderFee {
    @PrimaryGeneratedColumn({ type: 'bigint', unsigned: true })
    id: number

    @ManyToOne(() => Order, { nullable: false })
    @JoinColumn({ name: 'order_id' })
    order: Order

    @Column()
    asset: string

    @Column({
        default: '0',
        type: 'decimal',
        precision: 25,
        scale: 8,
        transformer: DecimalJsTransformer,
    })
    amount: string

    @Column({ type: 'enum', enum: FeeSource })
    source: FeeSource

    @CreateDateColumn()
    created_at: Date

    @UpdateDateColumn()
    updated_at: Date
}
